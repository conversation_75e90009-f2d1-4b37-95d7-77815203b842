"""
Metrics collection and management for model training.

This module provides classes for collecting, storing, and retrieving metrics
during model training and evaluation.
"""

from collections import defaultdict
from dataclasses import asdict, dataclass, field
from typing import Any, Dict, Generic, List, Optional, Tuple, TypeVar

import numpy as np

T = TypeVar("T")


class MetricsDict(defaultdict, Generic[T]):
    """A specialized defaultdict for metrics that provides additional functionality."""

    def get_latest(self, key: str, default: Any = None) -> Any:
        """Get the latest value for a metric."""
        values = self.get(key, [])
        if values and isinstance(values, list) and len(values) > 0:
            return values[-1]
        return default

    def get_max(self, key: str, default: Any = None) -> Any:
        """Get the maximum value for a metric."""
        values = self.get(key, [])
        if values and isinstance(values, list) and len(values) > 0:
            return max(values)
        return default

    def get_min(self, key: str, default: Any = None) -> Any:
        """Get the minimum value for a metric."""
        values = self.get(key, [])
        if values and isinstance(values, list) and len(values) > 0:
            return min(values)
        return default

    def get_mean(self, key: str, default: Any = None) -> Any:
        """Get the mean value for a metric."""
        values = self.get(key, [])
        if values and isinstance(values, list) and len(values) > 0:
            return np.mean(values)
        return default


@dataclass
class TrainingMetrics:
    """Container for training-specific metrics."""

    losses: List[float] = field(default_factory=list)
    accuracies: List[float] = field(default_factory=list)


@dataclass
class TestingMetrics:
    """Container for testing/validation-specific metrics."""

    losses: List[float] = field(default_factory=list)
    accuracies: List[float] = field(default_factory=list)


@dataclass
class ClassificationMetrics:
    """Container for classification-specific metrics."""

    precision: List[float] = field(default_factory=list)
    recall: List[float] = field(default_factory=list)
    f1_score: List[float] = field(default_factory=list)


@dataclass
class FinalTestMetrics:
    """Container for final test evaluation metrics on held-out test data."""

    loss: Optional[float] = None
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    evaluation_time: Optional[float] = None


@dataclass
class MetricsStorage:
    """Storage container for different types of metrics."""

    # Categorized metrics
    train: TrainingMetrics = field(default_factory=TrainingMetrics)
    test: TestingMetrics = field(default_factory=TestingMetrics)
    classification: ClassificationMetrics = field(default_factory=ClassificationMetrics)
    final_test: FinalTestMetrics = field(default_factory=FinalTestMetrics)

    # Performance metrics
    timing: MetricsDict[List[float]] = field(default_factory=lambda: MetricsDict(list))
    resources: MetricsDict[List[float]] = field(
        default_factory=lambda: MetricsDict(list)
    )


@dataclass
class Metrics:
    """
    A class to store and manage training metrics.

    This class provides methods for collecting, updating, and retrieving
    metrics during model training and evaluation.
    """

    # Core data storage
    epoch_metrics: List[Dict[str, Any]] = field(default_factory=list)
    batch_metrics: List[Dict[str, Any]] = field(default_factory=list)
    storage: MetricsStorage = field(default_factory=MetricsStorage)

    # Additional metrics
    custom_metrics: Dict[str, Any] = field(default_factory=dict)
    error: Optional[Dict[str, str]] = None

    # Backward compatibility properties
    @property
    def train(self) -> TrainingMetrics:
        """Access training metrics."""
        return self.storage.train

    @property
    def test(self) -> TestingMetrics:
        """Access test metrics."""
        return self.storage.test

    @property
    def classification(self) -> ClassificationMetrics:
        """Access classification metrics."""
        return self.storage.classification

    @property
    def final_test(self) -> FinalTestMetrics:
        """Access final test metrics."""
        return self.storage.final_test

    @property
    def timing(self) -> MetricsDict[List[float]]:
        """Access timing metrics."""
        return self.storage.timing

    @property
    def resources(self) -> MetricsDict[List[float]]:
        """Access resource metrics."""
        return self.storage.resources

    def __post_init__(self):
        """Ensure timing and resources are MetricsDicts even if initialized from a dict."""
        if not isinstance(self.storage.timing, MetricsDict):
            self.storage.timing = MetricsDict(list, self.storage.timing or {})
        if not isinstance(self.storage.resources, MetricsDict):
            self.storage.resources = MetricsDict(list, self.storage.resources or {})

    def add_epoch_metric(self, epoch_data: Dict[str, Any]) -> None:
        """Add metrics for a training epoch.

        Args:
            epoch_data: Dictionary of metrics for the epoch
        """
        self.epoch_metrics.append(epoch_data)

        # Extract standard metrics
        self._extract_training_metrics(epoch_data)
        self._extract_validation_test_metrics(epoch_data)
        self._extract_classification_metrics(epoch_data)
        self._extract_final_test_metrics(epoch_data)

    def _extract_training_metrics(self, epoch_data: Dict[str, Any]) -> None:
        """Extract training metrics from epoch data."""
        if "train_loss" in epoch_data:
            self.train.losses.append(epoch_data["train_loss"])
        if "train_accuracy" in epoch_data:
            self.train.accuracies.append(epoch_data["train_accuracy"])

    def _extract_validation_test_metrics(self, epoch_data: Dict[str, Any]) -> None:
        """Extract validation/test metrics from epoch data."""
        # Handle both validation_loss and test_loss for backward compatibility
        if "validation_loss" in epoch_data:
            self.test.losses.append(epoch_data["validation_loss"])
        elif "test_loss" in epoch_data:
            self.test.losses.append(epoch_data["test_loss"])

        # Handle both validation_accuracy and test_accuracy for backward compatibility
        if "validation_accuracy" in epoch_data:
            self.test.accuracies.append(epoch_data["validation_accuracy"])
        elif "test_accuracy" in epoch_data:
            self.test.accuracies.append(epoch_data["test_accuracy"])

    def _extract_classification_metrics(self, epoch_data: Dict[str, Any]) -> None:
        """Extract classification metrics from epoch data."""
        if "precision" in epoch_data:
            self.classification.precision.append(epoch_data["precision"])
        if "recall" in epoch_data:
            self.classification.recall.append(epoch_data["recall"])
        if "f1_score" in epoch_data:
            self.classification.f1_score.append(epoch_data["f1_score"])

    def _extract_final_test_metrics(self, epoch_data: Dict[str, Any]) -> None:
        """Extract final test metrics from epoch data (only set once at the end of training)."""
        if "final_test_loss" in epoch_data:
            self.final_test.loss = epoch_data["final_test_loss"]
        if "final_test_accuracy" in epoch_data:
            self.final_test.accuracy = epoch_data["final_test_accuracy"]
        if "final_test_precision" in epoch_data:
            self.final_test.precision = epoch_data["final_test_precision"]
        if "final_test_recall" in epoch_data:
            self.final_test.recall = epoch_data["final_test_recall"]
        if "final_test_f1_score" in epoch_data:
            self.final_test.f1_score = epoch_data["final_test_f1_score"]

    def add_batch_metric(self, metrics_dict: Dict[str, Any]) -> None:
        """Add metrics for a batch."""
        self.batch_metrics.append(metrics_dict.copy())

    def add_timing(self, key: str, value: float) -> None:
        """Add a timing metric."""
        self.timing[key].append(value)

    def add_resource(self, key: str, value: float) -> None:
        """Add a resource usage metric."""
        self.resources[key].append(value)

    def add_custom_metric(self, name: str, value: Any) -> None:
        """Add a custom metric."""
        if name not in self.custom_metrics:
            # Initialize based on whether we want to track history
            if isinstance(value, (int, float)) and not isinstance(value, bool):
                # For numeric values, we typically want to track history
                self.custom_metrics[name] = [value]
            else:
                # For non-numeric values, just store the value directly
                self.custom_metrics[name] = value
        else:
            # If it's already a list, append to it (for numeric values)
            if isinstance(self.custom_metrics[name], list):
                self.custom_metrics[name].append(value)
            else:
                # If it's not a list, replace it
                self.custom_metrics[name] = value

    def set_error(self, error_type: str, error_message: str) -> None:
        """Set error information."""
        self.error = {"type": error_type, "message": error_message}

    def get_metric_summary(self) -> Dict[str, Any]:
        """Returns a summary dictionary of key metrics."""
        summary = {}
        metrics = {}

        # Final training metrics
        if self.train.losses:
            metrics["train_loss"] = self.train.losses[-1]
        if self.train.accuracies:
            metrics["train_accuracy"] = self.train.accuracies[-1]
        if self.test.losses:
            metrics["test_loss"] = self.test.losses[-1]
        if self.test.accuracies:
            metrics["test_accuracy"] = self.test.accuracies[-1]

        # Classification metrics
        if self.classification.precision:
            metrics["precision"] = self.classification.precision[-1]
        if self.classification.recall:
            metrics["recall"] = self.classification.recall[-1]
        if self.classification.f1_score:
            metrics["f1_score"] = self.classification.f1_score[-1]

        if metrics:
            summary["final"] = metrics

        # Include timing information
        summary["timing"] = dict(self.timing)

        # Include error information if present
        if self.error:
            summary["error"] = self.error

        # Include resource metrics with summary statistics
        resource_summary = {}
        for key, values in self.resources.items():
            if values:
                resource_summary[f"{key}_max"] = max(values)
                resource_summary[f"{key}_mean"] = np.mean(values)
                resource_summary[f"{key}_last"] = values[-1]

        summary["resources"] = (
            resource_summary if resource_summary else dict(self.resources)
        )

        # Include custom metrics in the summary
        summary["custom_metrics"] = {
            k: v[-1] if isinstance(v, list) and v else v
            for k, v in self.custom_metrics.items()
        }

        return summary

    def get(self, key: str, default: Any = None) -> Any:
        """Get a metric by key, checking all attributes and custom_metrics."""
        result = default

        # Use a helper method to find the value in various locations
        found_value, is_found = self._find_metric_value(key)
        if is_found:
            result = found_value

        return result

    def _find_metric_value(self, key: str) -> Tuple[Any, bool]:
        """Find a metric value by key in various locations.

        Returns:
            Tuple containing (value, is_found)
        """
        # Direct attribute access for top-level fields
        if hasattr(self, key):
            value = getattr(self, key)
            # Prevent returning methods if a field has the same name as a method by chance
            if not callable(value):
                return value, True

        # Check for custom metrics
        if key in self.custom_metrics:
            # For custom metrics, return the last value if it's a list with a single element
            value = self.custom_metrics[key]
            if isinstance(value, list) and len(value) == 1:
                return value[0], True
            return value, True

        # Check nested dataclasses
        if value := self._check_nested_classes(key):
            return value, True

        # Check common nested dicts
        if value := self._check_nested_dicts(key):
            return value, True

        return None, False

    def _check_nested_classes(self, key: str) -> Optional[Any]:
        """Check for the key in nested dataclass attributes."""
        nested_classes = ["train", "test", "classification"]
        for class_name in nested_classes:
            nested_obj = getattr(self, class_name)
            # Check if the key matches a pattern like 'train_losses' -> 'train.losses'
            prefix = f"{class_name}_"
            if key.startswith(prefix):
                attr_name = key[len(prefix) :]
                if hasattr(nested_obj, attr_name):
                    return getattr(nested_obj, attr_name)
            # Direct attribute access in nested class
            if hasattr(nested_obj, key):
                return getattr(nested_obj, key)
        return None

    def _check_nested_dicts(self, key: str) -> Optional[Any]:
        """Check for the key in nested dictionary attributes."""
        for dict_name in ["timing", "resources"]:
            nested_dict = getattr(self, dict_name, None)
            if isinstance(nested_dict, dict) and key in nested_dict:
                return nested_dict[key]
        return None

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to a dictionary for serialization with backward compatibility."""
        # Create the base dictionary
        metrics_dict = {
            "epoch_metrics": self.epoch_metrics,
            "batch_metrics": self.batch_metrics,
            "custom_metrics": self.custom_metrics,
            "error": self.error,
        }

        # Add categorized metrics in flat structure for backward compatibility
        metrics_dict.update(
            {
                "train": asdict(self.storage.train),
                "test": asdict(self.storage.test),
                "classification": asdict(self.storage.classification),
                "final_test": asdict(self.storage.final_test),
                "timing": dict(self.storage.timing),
                "resources": dict(self.storage.resources),
            }
        )

        return metrics_dict

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Metrics":
        """Create a Metrics instance from a dictionary.

        Only supports the new format with nested dataclasses.
        """
        # Create a new instance with default values
        metrics = cls()

        # Process different parts of the metrics data
        cls._process_basic_fields(metrics, data)
        cls._process_dictionary_fields(metrics, data)
        cls._process_nested_metrics(metrics, data)

        return metrics

    @classmethod
    def _process_basic_fields(cls, metrics: "Metrics", data: Dict[str, Any]) -> None:
        """Process basic fields from the data dictionary."""
        if "epoch_metrics" in data and isinstance(data["epoch_metrics"], list):
            metrics.epoch_metrics = data["epoch_metrics"]
        if "batch_metrics" in data and isinstance(data["batch_metrics"], list):
            metrics.batch_metrics = data["batch_metrics"]
        if "error" in data:
            metrics.error = data["error"]

    @classmethod
    def _process_dictionary_fields(
        cls, metrics: "Metrics", data: Dict[str, Any]
    ) -> None:
        """Process dictionary fields from the data dictionary."""
        for field_name in ["timing", "resources", "custom_metrics"]:
            if field_name in data and isinstance(data[field_name], dict):
                getattr(metrics, field_name).update(data[field_name])

    @classmethod
    def _process_nested_metrics(cls, metrics: "Metrics", data: Dict[str, Any]) -> None:
        """Process nested metrics structures from the data dictionary."""
        # Process train metrics
        cls._process_metric_group(
            metrics.train, data.get("train", {}), ["losses", "accuracies"]
        )

        # Process test metrics
        cls._process_metric_group(
            metrics.test, data.get("test", {}), ["losses", "accuracies"]
        )

        # Process classification metrics
        cls._process_metric_group(
            metrics.classification,
            data.get("classification", {}),
            ["precision", "recall", "f1_score"],
        )

    @staticmethod
    def _process_metric_group(
        target_obj: Any, source_dict: Dict[str, Any], field_names: List[str]
    ) -> None:
        """Process a group of metrics from source dictionary to target object."""
        if not isinstance(source_dict, dict):
            return

        for field_name in field_names:
            if field_name in source_dict and isinstance(source_dict[field_name], list):
                setattr(target_obj, field_name, source_dict[field_name])
