"""
Metrics plotting callback for automatic generation of training plots.

This callback automatically generates comprehensive plots at the end of training,
including:
- Training and validation loss plots
- Training and validation accuracy plots
- Classification metrics plots (precision, recall, F1-score)
- Resource usage plots (CPU, memory, GPU)
- Timing metrics plots
"""

import logging
from typing import Any, Dict

from src.train.callbacks.base import Callback
from src.utils.plots import (
    plot_accuracy,
    plot_classification_metrics,
    plot_losses,
    plot_resource_usage,
    plot_timing_metrics,
)


class MetricsPlottingCallback(Callback):
    """
    Callback that automatically generates comprehensive metrics plots at the end of training.

    This callback ensures that all training metrics are visualized and saved to the
    appropriate plots directory, making it easy to analyze training performance.
    """

    def __init__(self, model_run_uuid: str):
        """
        Initialize the metrics plotting callback.

        Args:
            model_run_uuid: The UUID of the model run for plot path generation
        """
        super().__init__()
        self.model_run_uuid = model_run_uuid
        self.logger = None  # Will be set to trainer's logger when trainer is set

    def on_trainer_set(self) -> None:
        """Called when the trainer is set."""
        if self.trainer and hasattr(self.trainer, "logger"):
            self.logger = self.trainer.logger
        else:
            self.logger = logging.getLogger(__name__)

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """
        Called at the end of training to generate all metrics plots.

        Args:
            logs: Optional logs dictionary (not used in this callback)
        """
        if self.trainer is None:
            return

        if self.logger:
            self.logger.info(
                "Generating metrics plots for model run %s", self.model_run_uuid
            )

        try:
            self._generate_loss_plots()
            self._generate_accuracy_plots()
            self._generate_classification_metrics_plots()
            self._generate_resource_usage_plots()
            self._generate_timing_plots()

            if self.logger:
                self.logger.info("Successfully generated all metrics plots")

        except Exception as e:
            if self.logger:
                self.logger.error("Error generating metrics plots: %s", e)
            else:
                print(f"Error generating metrics plots: {e}")

    def _generate_loss_plots(self) -> None:
        """Generate training and validation loss plots."""
        metrics = self.trainer.metrics

        if (
            hasattr(metrics.train, "losses")
            and metrics.train.losses
            and hasattr(metrics.test, "losses")
            and metrics.test.losses
        ):

            plot_losses(
                train_loss=metrics.train.losses,
                test_loss=metrics.test.losses,
                model_run_uuid=self.model_run_uuid,
            )

            if self.logger:
                self.logger.debug("Generated loss plots")

    def _generate_accuracy_plots(self) -> None:
        """Generate training and validation accuracy plots."""
        metrics = self.trainer.metrics

        if (
            hasattr(metrics.train, "accuracies")
            and metrics.train.accuracies
            and hasattr(metrics.test, "accuracies")
            and metrics.test.accuracies
        ):

            plot_accuracy(
                train_accuracy=metrics.train.accuracies,
                test_accuracy=metrics.test.accuracies,
                model_run_uuid=self.model_run_uuid,
            )

            if self.logger:
                self.logger.debug("Generated accuracy plots")

    def _generate_classification_metrics_plots(self) -> None:
        """Generate classification metrics plots (precision, recall, F1-score)."""
        metrics = self.trainer.metrics

        if self._has_classification_metrics(metrics):
            plot_classification_metrics(
                precision=metrics.classification.precision,
                recall=metrics.classification.recall,
                f1_score=metrics.classification.f1_score,
                model_run_uuid=self.model_run_uuid,
            )

            if self.logger:
                self.logger.debug("Generated classification metrics plots")

    def _generate_resource_usage_plots(self) -> None:
        """Generate resource usage plots (CPU, memory, GPU)."""
        metrics = self.trainer.metrics

        if hasattr(metrics, "resources") and metrics.resources:
            # Check if we have any resource metrics to plot
            has_metrics = any(
                key in metrics.resources and metrics.resources[key]
                for key in ["cpu_percent", "memory_percent", "gpu_memory_used"]
            )

            if has_metrics:
                plot_resource_usage(
                    resources_dict=metrics.resources, model_run_uuid=self.model_run_uuid
                )

                if self.logger:
                    self.logger.debug("Generated resource usage plots")

    def _generate_timing_plots(self) -> None:
        """Generate timing metrics plots."""
        metrics = self.trainer.metrics

        if hasattr(metrics, "timing") and metrics.timing:
            # Check if we have any timing metrics to plot
            has_metrics = any(
                key in metrics.timing and metrics.timing[key]
                for key in ["train_times", "validation_times"]
            )

            if has_metrics:
                plot_timing_metrics(
                    timing_dict=metrics.timing, model_run_uuid=self.model_run_uuid
                )

                if self.logger:
                    self.logger.debug("Generated timing plots")

    def _has_classification_metrics(self, metrics) -> bool:
        """Check if classification metrics are available for plotting."""
        if not hasattr(metrics, "classification") or not metrics.classification:
            return False

        classification = metrics.classification
        return (
            hasattr(classification, "precision")
            and classification.precision
            and hasattr(classification, "recall")
            and classification.recall
            and hasattr(classification, "f1_score")
            and classification.f1_score
        )
