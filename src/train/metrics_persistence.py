"""
Metrics persistence functionality for saving and loading training metrics.

This module provides a class for persisting metrics to files and loading them
back, abstracting the storage details from the metrics collection logic.
"""

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

from src.config.paths import get_run_paths
from src.train.metrics import Metrics


class MetricsPersistence:
    """
    A class to handle saving and loading metrics to/from files.

    This class abstracts the persistence logic for metrics, allowing
    metrics to be saved to and loaded from various formats and locations.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the MetricsPersistence.

        Args:
            logger: Optional logger instance for logging operations
        """
        self.logger = logger or logging.getLogger(__name__)

    def save_metrics(
        self,
        metrics: Metrics,
        output_dir: Union[str, Path],
    ) -> Dict[str, Path]:
        """
        Save metrics to files using the centralized path helpers.

        Args:
            metrics: The Metrics instance to save.
            output_dir: The run's root output directory.

        Returns:
            Dictionary mapping file types to their paths.
        """
        output_dir = Path(output_dir)
        model_run_uuid = output_dir.name

        run_paths = get_run_paths(model_run_uuid)
        history_path = Path(run_paths.metrics_history)
        summary_path = Path(run_paths.metrics_summary)

        # Ensure the target directory exists
        target_dir = history_path.parent
        target_dir.mkdir(parents=True, exist_ok=True)

        saved_files = {}

        # Save detailed history
        with open(history_path, "w", encoding="utf-8") as f:
            json.dump(metrics.to_dict(), f, indent=4, default=self._json_serialize)
        self.logger.info("Saved detailed metrics history to %s", history_path)
        saved_files["history"] = history_path

        # Save summary
        with open(summary_path, "w", encoding="utf-8") as f:
            summary_data = metrics.get_metric_summary()
            json.dump(summary_data, f, indent=4, default=self._json_serialize)
        self.logger.info("Saved metrics summary to %s", summary_path)
        saved_files["summary"] = summary_path

        return saved_files

    def load_metrics(
        self, metrics_path: Union[str, Path], metrics_instance: Optional[Metrics] = None
    ) -> Metrics:
        """
        Load metrics from a file.

        Args:
            metrics_path: Path to the metrics file to load
            metrics_instance: Optional existing Metrics instance to update

        Returns:
            Loaded Metrics instance
        """
        # Ensure we have a valid metrics instance to work with
        if metrics_instance is None:
            metrics_instance = Metrics()

        metrics_path = Path(metrics_path)

        if not metrics_path.exists():
            self.logger.error("Metrics file not found: %s", metrics_path)
            raise FileNotFoundError(f"Metrics file not found: {metrics_path}")

        try:
            with open(metrics_path, "r", encoding="utf-8") as f:
                metrics_data = json.load(f)

            # Update the metrics instance with the loaded data
            self._update_metrics_from_dict(metrics_instance, metrics_data)
            return metrics_instance

        except json.JSONDecodeError as e:
            self.logger.error(
                "Invalid JSON format in metrics file %s: %s", metrics_path, e
            )
            raise ValueError(
                f"Invalid JSON format in metrics file {metrics_path}: {e}"
            ) from e
        except Exception as e:
            self.logger.error("Error loading metrics from %s: %s", metrics_path, e)
            raise RuntimeError(f"Error loading metrics from {metrics_path}: {e}") from e

    def update_metrics(self, metrics: Metrics, data: Dict[str, Any]) -> None:
        """Update a metrics instance from a dictionary.

        Args:
            metrics: The Metrics instance to update
            data: Dictionary containing metrics data
        """
        # Delegate to the internal implementation
        self._update_metrics_from_dict(metrics, data)

    def _update_metrics_from_dict(self, metrics: Metrics, data: Dict[str, Any]) -> None:
        """Internal method to update a metrics instance from a dictionary."""
        # Process different parts of the metrics data
        self._update_basic_fields(metrics, data)
        self._update_legacy_classification_metrics(metrics, data)
        self._update_nested_metrics(metrics, data)
        self._update_dictionary_fields(metrics, data)

        # Handle error field separately as it's a simple assignment
        if "error" in data:
            metrics.error = data["error"]

    def _update_basic_fields(self, metrics: Metrics, data: Dict[str, Any]) -> None:
        """Update basic top-level fields in the metrics instance."""
        for field in ["epoch_metrics", "batch_metrics"]:
            if field in data and isinstance(data[field], list):
                setattr(metrics, field, data[field])

    def _update_legacy_classification_metrics(
        self, metrics: Metrics, data: Dict[str, Any]
    ) -> None:
        """Update classification metrics from top-level fields (backward compatibility)."""
        for field in ["precision", "recall", "f1_score"]:
            if field in data and isinstance(data[field], list):
                setattr(metrics.classification, field, data[field])

    def _update_nested_metrics(self, metrics: Metrics, data: Dict[str, Any]) -> None:
        """Update nested metrics structures from the data dictionary."""
        # Process train metrics
        self._update_metric_group(
            metrics.train, data.get("train", {}), ["losses", "accuracies"]
        )

        # Process test metrics
        self._update_metric_group(
            metrics.test, data.get("test", {}), ["losses", "accuracies"]
        )

        # Process classification metrics
        self._update_metric_group(
            metrics.classification,
            data.get("classification", {}),
            ["precision", "recall", "f1_score"],
        )

    def _update_metric_group(
        self, target_obj: Any, source_dict: Dict[str, Any], field_names: List[str]
    ) -> None:
        """Update a group of metrics from source dictionary to target object."""
        if not isinstance(source_dict, dict):
            return

        for field_name in field_names:
            if field_name in source_dict and isinstance(source_dict[field_name], list):
                setattr(target_obj, field_name, source_dict[field_name])

    def _update_dictionary_fields(self, metrics: Metrics, data: Dict[str, Any]) -> None:
        """Update dictionary fields in the metrics instance."""
        # Handle standard dictionaries
        for dict_name in ["timing", "resources"]:
            if dict_name in data and isinstance(data[dict_name], dict):
                nested_dict = getattr(metrics, dict_name)
                nested_dict.update(data[dict_name])

        # Handle custom metrics with special merging logic
        if "custom_metrics" in data and isinstance(data["custom_metrics"], dict):
            self._merge_custom_metrics(metrics, data["custom_metrics"])

    def _merge_custom_metrics(self, metrics: Metrics, updates: Dict[str, Any]) -> None:
        """Merge custom metrics with intelligent type handling."""
        for key, value in updates.items():
            current = metrics.custom_metrics.get(key)
            if current is None:
                metrics.custom_metrics[key] = value
            elif isinstance(current, list) and not isinstance(value, list):
                current.append(value)
            elif not isinstance(current, list) and isinstance(value, list):
                metrics.custom_metrics[key] = value
            elif isinstance(current, list) and isinstance(value, list):
                current.extend(value)
            else:
                metrics.custom_metrics[key] = value

    def _json_serialize(self, obj: Any) -> Any:
        """
        Custom JSON serializer for handling non-serializable objects.

        Args:
            obj: Object to serialize

        Returns:
            JSON serializable representation of the object
        """
        # Handle mock objects to prevent recursion issues in tests
        if hasattr(obj, "_mock_name") or str(type(obj)).find("Mock") != -1:
            return f"<Mock object: {type(obj).__name__}>"

        if isinstance(obj, (set, frozenset)):
            return list(obj)
        if hasattr(obj, "tolist") and callable(obj.tolist):  # For numpy arrays
            try:
                return obj.tolist()
            except (RecursionError, AttributeError):
                # Fallback for problematic objects
                return str(obj)
        if hasattr(obj, "__dict__"):  # For custom objects
            return obj.__dict__

        # Default: convert to string
        return str(obj)
