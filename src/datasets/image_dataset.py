"""
ImageDataset for loading images from the local filesystem.

This dataset class loads real images from the filesystem based on dataset_sets
data from the database, handling different set types and coin_side_uuid organization.
"""

import logging
import threading
import time
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import torch
from PIL import Image
from pillow_heif import register_heif_opener
from torch.utils.data import Dataset
from torchvision import transforms

from config.paths import get_background_dir_name
from database.models.dataset_set import DatasetSet
from utils.image_utils import (
    build_image_path,
    find_image_file_with_extensions,
    get_supported_image_extensions,
)

logger = logging.getLogger(__name__)


@dataclass
class FormatPerformanceMetrics:
    """Track performance by image format."""

    format_loading_times: Dict[str, List[float]] = field(default_factory=dict)
    format_memory_usage: Dict[str, List[float]] = field(default_factory=dict)
    format_error_rates: Dict[str, int] = field(default_factory=dict)
    format_file_sizes: Dict[str, List[float]] = field(default_factory=dict)


@dataclass
class ImageDatasetConfig:
    """Configuration for ImageDataset with multi-format support."""

    default_image_size: Tuple[int, int] = (224, 224)
    # Multi-format support
    supported_extensions: List[str] = None
    quality_threshold: int = 85  # Minimum JPEG quality
    max_file_size_mb: int = 10  # Prevent huge files
    transform: Optional[transforms.Compose] = None
    target_transform: Optional[transforms.Compose] = None
    include_background_class: bool = True
    background_class_name: str = get_background_dir_name()

    def __post_init__(self):
        """Initialize default supported extensions if not provided."""
        if self.supported_extensions is None:
            self.supported_extensions = get_supported_image_extensions()


class ImageLoadingError(Exception):
    """Exception raised for image loading errors."""


class ImageDataset(Dataset):
    """
    Dataset for loading images from the filesystem based on dataset_sets data.

    This dataset loads images organized by coin_side_uuid and handles different
    set types (TRAIN, VALIDATION, TEST) from the database.
    """

    def __init__(
        self,
        dataset_sets: List[DatasetSet],
        images_base_dir: Path,
        config: Optional[ImageDatasetConfig] = None,
    ):
        """
        Initialize the image dataset.

        Args:
            dataset_sets: List of DatasetSet records from the database
            images_base_dir: Base directory containing the images
            config: Optional config for dataset settings (includes transforms, image size, etc.)
        """
        self.dataset_sets = dataset_sets
        self.images_base_dir = Path(images_base_dir)

        # Use provided config or create default
        self.config = config or ImageDatasetConfig()

        # Initialize performance metrics for tracking loading performance
        self.performance_metrics = FormatPerformanceMetrics()

        # Thread-safe lock for performance metrics updates
        self._metrics_lock = threading.Lock()

        # Build label mapping from coin_side_uuid to integer labels
        self.coin_side_to_label = self._build_label_mapping()

        # Filter out invalid dataset sets and build image paths together
        self.valid_dataset_sets, self.image_paths = self._validate_and_build_paths()

        logger.info(
            "Initialized ImageDataset with %d valid images from %d dataset sets",
            len(self.valid_dataset_sets),
            len(dataset_sets),
        )

    def _build_label_mapping(self) -> dict:
        """
        Build a mapping from coin_side_uuid to integer labels.

        If include_background_class is True, images with null coin_side_uuid
        will be assigned to a special background class.

        Returns:
            Dictionary mapping coin_side_uuid (or background class name) to integer labels
        """
        unique_coin_sides = set()
        has_background_images = False

        for dataset_set in self.dataset_sets:
            if dataset_set.coin_side_uuid:
                unique_coin_sides.add(str(dataset_set.coin_side_uuid))
            elif self.config.include_background_class:
                has_background_images = True

        # Sort for consistent label assignment
        sorted_coin_sides = sorted(unique_coin_sides)
        coin_side_to_label = {
            coin_side: idx for idx, coin_side in enumerate(sorted_coin_sides)
        }

        # Add background class if we have background images and it's enabled
        if has_background_images and self.config.include_background_class:
            coin_side_to_label[self.config.background_class_name] = len(
                sorted_coin_sides
            )

        logger.info(
            "Created label mapping for %d coin sides%s: %s",
            len(unique_coin_sides),
            (
                " + background class"
                if has_background_images and self.config.include_background_class
                else ""
            ),
            list(coin_side_to_label.keys())[:5]
            + (["..."] if len(coin_side_to_label) > 5 else []),
        )

        return coin_side_to_label

    def _validate_dataset_sets(self) -> List[DatasetSet]:
        """
        Filter out dataset sets that don't have required fields.

        Now includes background images (null coin_side_uuid) if background class is enabled.

        Returns:
            List of valid dataset sets
        """
        valid_sets = []
        for dataset_set in self.dataset_sets:
            # Check if we have a valid image_uuid (required for all images)
            if not dataset_set.image_uuid:
                logger.warning(
                    "Skipping dataset set without image_uuid: coin_side_uuid=%s",
                    dataset_set.coin_side_uuid,
                )
                continue

            # Check if it's a labeled image (has coin_side_uuid)
            if dataset_set.coin_side_uuid:
                if str(dataset_set.coin_side_uuid) in self.coin_side_to_label:
                    valid_sets.append(dataset_set)
                else:
                    logger.warning(
                        "Skipping dataset set with unknown coin_side_uuid: %s",
                        dataset_set.coin_side_uuid,
                    )
            # Check if it's a background image (null coin_side_uuid) and background class is enabled
            elif (
                self.config.include_background_class
                and self.config.background_class_name in self.coin_side_to_label
            ):
                valid_sets.append(dataset_set)
            else:
                logger.warning(
                    "Skipping unlabeled dataset set (background class disabled): image_uuid=%s",
                    dataset_set.image_uuid,
                )

        return valid_sets

    def _validate_and_build_paths(self) -> Tuple[List[DatasetSet], List[Path]]:
        """
        Validate dataset sets and build image paths simultaneously.

        This ensures that valid_dataset_sets and image_paths stay in sync.
        Background images (null coin_side_uuid) are stored in a special 'background' directory.
        File extensions are determined first from URL, then trying all supported extensions.

        Returns:
            Tuple of (valid_dataset_sets, image_paths)
        """
        # First get the basic validation from the parent method
        valid_sets = self._validate_dataset_sets()

        final_valid_sets = []
        image_paths = []

        for dataset_set in valid_sets:
            # Skip dataset sets without image URLs
            if not dataset_set.image_url:
                logger.warning(
                    "Skipping dataset set without image_url: image_uuid=%s, coin_side_uuid=%s",
                    dataset_set.image_uuid,
                    dataset_set.coin_side_uuid,
                )
                continue

            try:
                # First try URL-based path construction (extract extension from URL)
                image_path = build_image_path(
                    base_dir=self.images_base_dir,
                    image_uuid=str(dataset_set.image_uuid),
                    coin_side_uuid=(
                        str(dataset_set.coin_side_uuid)
                        if dataset_set.coin_side_uuid
                        else None
                    ),
                    image_url=dataset_set.image_url,
                    background_class_name=self.config.background_class_name,
                )

                # Check if the URL-based path exists
                if not image_path.exists():
                    # Fallback to smart format detection (try all supported extensions)
                    fallback_path = find_image_file_with_extensions(
                        base_dir=self.images_base_dir,
                        image_uuid=str(dataset_set.image_uuid),
                        coin_side_uuid=(
                            str(dataset_set.coin_side_uuid)
                            if dataset_set.coin_side_uuid
                            else None
                        ),
                        supported_extensions=self.config.supported_extensions,
                        background_class_name=self.config.background_class_name,
                    )
                    if fallback_path is not None:
                        image_path = fallback_path

                # Only add to final lists if we successfully built the path
                final_valid_sets.append(dataset_set)
                image_paths.append(image_path)

            except ValueError as e:
                logger.warning("Skipping dataset set with invalid image_url: %s", e)
                continue

        return final_valid_sets, image_paths

    def __len__(self) -> int:
        """Return the number of samples in the dataset."""
        return len(self.valid_dataset_sets)

    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Get a sample from the dataset.

        Args:
            idx: Index of the sample to retrieve

        Returns:
            Tuple of (image, label) after applying transforms
        """
        if idx >= len(self.valid_dataset_sets):
            raise IndexError(
                f"Index {idx} out of range for dataset of size {len(self)}"
            )

        dataset_set = self.valid_dataset_sets[idx]
        image_path = self.image_paths[idx]

        # Load image with performance tracking
        start_time = time.time()
        success = True
        try:
            image = self._load_image(image_path)
        except Exception as e:
            logger.error("Failed to load image %s: %s", image_path, str(e))
            success = False
            # Return a black image as fallback
            image = Image.new("RGB", self.config.default_image_size, color="black")
        finally:
            loading_time = time.time() - start_time
            self._track_loading_performance(image_path, loading_time, success)

        # Get label - handle both labeled and background images
        if dataset_set.coin_side_uuid:
            label = self.coin_side_to_label[str(dataset_set.coin_side_uuid)]
        else:
            # Background image
            label = self.coin_side_to_label[self.config.background_class_name]
        label = torch.tensor(label, dtype=torch.long)

        # Apply transforms
        if self.config.transform is not None:
            image = self.config.transform(image)
        else:
            # Default transform: convert to tensor and resize
            default_transform = transforms.Compose(
                [
                    transforms.Resize(self.config.default_image_size),
                    transforms.ToTensor(),
                ]
            )
            image = default_transform(image)

        if self.config.target_transform is not None:
            label = self.config.target_transform(label)

        return image, label

    def _load_heic_image(self, image_path: Path) -> Image.Image:
        """Load HEIC image with error handling."""
        try:
            register_heif_opener()
            return Image.open(image_path)
        except ImportError as e:
            raise ImageLoadingError(
                f"HEIC support not available. Install pillow-heif to load {image_path}. "
                f"Run: pip install pillow-heif"
            ) from e
        except Exception as e:
            # Handle other HEIC-specific errors (corrupted files, unsupported variants)
            raise ImageLoadingError(
                f"Failed to load HEIC image {image_path}. "
                f"File may be corrupted or use unsupported HEIC variant: {str(e)}"
            ) from e

    def _load_webp_image(self, image_path: Path) -> Image.Image:
        """Load WebP image with optimizations."""
        try:
            image = Image.open(image_path)
            # WebP images might have transparency, handle appropriately
            if image.mode in ("RGBA", "LA"):
                # Create a white background for transparent images
                background = Image.new("RGB", image.size, (255, 255, 255))
                if image.mode == "RGBA":
                    background.paste(
                        image, mask=image.split()[-1]
                    )  # Use alpha channel as mask
                else:
                    background.paste(image)
                return background
            return image
        except Exception as e:
            raise ImageLoadingError(
                f"Failed to load WebP image {image_path}: {str(e)}"
            ) from e

    def _load_image_optimized(self, image_path: Path) -> Image.Image:
        """
        Load image with format-specific optimizations.

        Args:
            image_path: Path to the image file

        Returns:
            PIL Image object

        Raises:
            ImageLoadingError: If image cannot be loaded
        """
        if not image_path.exists():
            raise ImageLoadingError(f"Image file not found: {image_path}")

        # Check file size before loading to prevent memory issues
        file_size_mb = image_path.stat().st_size / (1024 * 1024)
        if file_size_mb > self.config.max_file_size_mb:
            logger.warning(
                "Image %s is %.2f MB, exceeding limit of %d MB. Loading but consider optimization.",
                image_path,
                file_size_mb,
                self.config.max_file_size_mb,
            )

        ext = image_path.suffix.lower().lstrip(".")

        try:
            if ext in ["heic", "heif"]:
                # Use specialized HEIC loader with hardware acceleration if available
                image = self._load_heic_image(image_path)
            elif ext == "webp":
                # WebP-specific optimizations
                image = self._load_webp_image(image_path)
            else:
                # Standard PIL loading for JPEG, PNG
                image = Image.open(image_path)

            # Validate image dimensions to prevent memory issues
            if image.size[0] * image.size[1] > 50_000_000:  # 50MP limit
                logger.warning(
                    "Image %s has very large dimensions %s, consider resizing for performance",
                    image_path,
                    image.size,
                )

            # Convert to RGB if necessary (handles grayscale, RGBA, etc.)
            if image.mode != "RGB":
                image = image.convert("RGB")
            return image
        except ImageLoadingError:
            # Re-raise our custom errors
            raise
        except Exception as e:
            raise ImageLoadingError(
                f"Failed to load image {image_path}: {str(e)}"
            ) from e

    def _load_image(self, image_path: Path) -> Image.Image:
        """
        Load an image from the filesystem.

        Args:
            image_path: Path to the image file

        Returns:
            PIL Image object

        Raises:
            ImageLoadingError: If image cannot be loaded
        """
        return self._load_image_optimized(image_path)

    def get_dataset_info(self) -> dict:
        """
        Get information about the dataset.

        Returns:
            Dictionary containing dataset statistics and information
        """
        # Count images per coin side (including background)
        coin_side_counts = {}
        for dataset_set in self.valid_dataset_sets:
            if dataset_set.coin_side_uuid:
                coin_side = str(dataset_set.coin_side_uuid)
            else:
                coin_side = self.config.background_class_name
            coin_side_counts[coin_side] = coin_side_counts.get(coin_side, 0) + 1

        return {
            "num_samples": len(self),
            "num_coin_sides": len(self.coin_side_to_label),
            "coin_side_counts": coin_side_counts,
            "images_base_dir": str(self.images_base_dir),
            "default_image_size": self.config.default_image_size,
            "supported_extensions": self.config.supported_extensions,
            "has_transform": self.config.transform is not None,
            "has_target_transform": self.config.target_transform is not None,
        }

    def get_num_classes(self) -> int:
        """
        Get the number of classes in the dataset.

        Returns:
            Number of unique classes (coin sides) in the dataset
        """
        return len(self.coin_side_to_label)

    def get_label_mapping(self) -> dict:
        """
        Get the label mapping from coin_side_uuid to integer labels.

        Returns:
            Dictionary mapping coin_side_uuid to integer labels
        """
        return self.coin_side_to_label.copy()

    def get_class_names(self) -> List[str]:
        """
        Get the list of class names (coin_side_uuid values) in label order.

        Returns:
            List of coin_side_uuid values sorted by their integer labels
        """
        return [
            coin_side
            for coin_side, _ in sorted(
                self.coin_side_to_label.items(), key=lambda x: x[1]
            )
        ]

    def get_coin_side_labels(self) -> dict:
        """
        Get the mapping from coin_side_uuid to integer labels.

        Returns:
            Dictionary mapping coin_side_uuid to integer labels
        """
        return self.coin_side_to_label.copy()

    def update_transform(self, transform: Optional[transforms.Compose]) -> None:
        """
        Update the transform pipeline for this dataset.

        Args:
            transform: New transform pipeline to apply
        """
        self.config.transform = transform

    def check_image_availability(self) -> Tuple[int, List[str]]:
        """
        Check how many images are actually available on disk.

        Returns:
            Tuple of (available_count, missing_paths)
        """
        available_count = 0
        missing_paths = []

        for image_path in self.image_paths:
            if image_path.exists():
                available_count += 1
            else:
                missing_paths.append(str(image_path))

        logger.info(
            "Image availability check: %d/%d images available, %d missing",
            available_count,
            len(self.image_paths),
            len(missing_paths),
        )

        return available_count, missing_paths

    @property
    def images(self) -> torch.Tensor:
        """
        Get all images as a tensor (for compatibility with existing code).

        Note: This loads all images into memory and should be used carefully.
        """
        logger.warning(
            "Loading all images into memory - this may consume significant RAM"
        )

        all_images = []
        for sample in self:
            image, _ = sample
            all_images.append(image.unsqueeze(0))

        return torch.cat(all_images, dim=0)

    @property
    def labels(self) -> torch.Tensor:
        """
        Get all labels as a tensor (for compatibility with existing code).
        """
        all_labels = []
        for dataset_set in self.valid_dataset_sets:
            if dataset_set.coin_side_uuid:
                label = self.coin_side_to_label[str(dataset_set.coin_side_uuid)]
            else:
                label = self.coin_side_to_label[self.config.background_class_name]
            all_labels.append(label)

        return torch.tensor(all_labels, dtype=torch.long)

    def validate_dataset_formats(self) -> Dict[str, Any]:
        """
        Validate and report on image formats in dataset.

        Returns:
            Dictionary containing format validation results
        """
        format_stats = {}
        corrupted_files = []
        unsupported_formats = []
        large_files = []

        for i, dataset_set in enumerate(self.valid_dataset_sets):
            image_path = self.image_paths[i]

            if not image_path.exists():
                unsupported_formats.append(str(dataset_set.image_uuid))
                continue

            try:
                with Image.open(image_path) as img:
                    format_name = img.format or "Unknown"
                    format_stats[format_name] = format_stats.get(format_name, 0) + 1

                    # Check file size
                    file_size_mb = image_path.stat().st_size / (1024 * 1024)
                    if file_size_mb > self.config.max_file_size_mb:
                        large_files.append(
                            {
                                "path": str(image_path),
                                "size_mb": file_size_mb,
                                "format": format_name,
                            }
                        )

            except Exception as e:
                corrupted_files.append({"path": str(image_path), "error": str(e)})

        # Generate recommendations based on findings
        recommendations = []
        if corrupted_files:
            recommendations.append(
                f"Found {len(corrupted_files)} corrupted files. "
                "Consider re-downloading or replacing these images."
            )
        if unsupported_formats:
            recommendations.append(
                f"Found {len(unsupported_formats)} images with unsupported formats. "
                "Check if files exist and have valid extensions."
            )
        if large_files:
            recommendations.append(
                f"Found {len(large_files)} large files (>{self.config.max_file_size_mb}MB). "
                "Consider compressing or resizing for better performance."
            )

        return {
            "format_distribution": format_stats,
            "corrupted_files": corrupted_files,
            "unsupported_formats": unsupported_formats,
            "large_files": large_files,
            "total_images": len(self.valid_dataset_sets),
            "recommendations": recommendations,
            "health_score": self._calculate_dataset_health_score(
                len(self.valid_dataset_sets),
                len(corrupted_files),
                len(unsupported_formats),
                len(large_files),
            ),
        }

    def _calculate_dataset_health_score(
        self,
        total_images: int,
        corrupted_count: int,
        unsupported_count: int,
        large_files_count: int,
    ) -> float:
        """
        Calculate a health score for the dataset (0.0 to 1.0).

        Args:
            total_images: Total number of images in dataset
            corrupted_count: Number of corrupted files
            unsupported_count: Number of unsupported format files
            large_files_count: Number of oversized files

        Returns:
            Health score between 0.0 (poor) and 1.0 (excellent)
        """
        if total_images == 0:
            return 0.0

        # Calculate penalty factors
        corrupted_penalty = (
            corrupted_count / total_images * 0.5
        )  # 50% penalty for corrupted files
        unsupported_penalty = (
            unsupported_count / total_images * 0.3
        )  # 30% penalty for unsupported
        large_files_penalty = (
            large_files_count / total_images * 0.1
        )  # 10% penalty for large files

        # Calculate health score (1.0 - total penalties)
        health_score = max(
            0.0, 1.0 - corrupted_penalty - unsupported_penalty - large_files_penalty
        )
        return round(health_score, 3)

    def _track_loading_performance(
        self, image_path: Path, loading_time: float, success: bool
    ):
        """Track loading performance by format with thread safety."""
        format_name = image_path.suffix.lower().lstrip(".")

        # Use thread-safe lock for metrics updates
        with self._metrics_lock:
            if success:
                self.performance_metrics.format_loading_times.setdefault(
                    format_name, []
                ).append(loading_time)
                file_size_mb = image_path.stat().st_size / (1024 * 1024)
                self.performance_metrics.format_file_sizes.setdefault(
                    format_name, []
                ).append(file_size_mb)
            else:
                self.performance_metrics.format_error_rates[format_name] = (
                    self.performance_metrics.format_error_rates.get(format_name, 0) + 1
                )

    def get_format_performance_report(self) -> Dict[str, Any]:
        """
        Get performance report by format.

        Returns:
            Dictionary containing performance statistics by format
        """
        metrics = self.performance_metrics

        # Check if any performance data has been collected
        if not metrics.format_loading_times and not metrics.format_error_rates:
            return {"message": "No performance data available"}

        report = {}
        for format_name in set(
            list(metrics.format_loading_times.keys())
            + list(metrics.format_error_rates.keys())
        ):
            loading_times = metrics.format_loading_times.get(format_name, [])
            file_sizes = metrics.format_file_sizes.get(format_name, [])
            error_count = metrics.format_error_rates.get(format_name, 0)

            report[format_name] = {
                "avg_loading_time": (
                    sum(loading_times) / len(loading_times) if loading_times else 0
                ),
                "min_loading_time": min(loading_times) if loading_times else 0,
                "max_loading_time": max(loading_times) if loading_times else 0,
                "avg_file_size_mb": (
                    sum(file_sizes) / len(file_sizes) if file_sizes else 0
                ),
                "total_files": len(loading_times),
                "error_count": error_count,
                "success_rate": (
                    len(loading_times) / (len(loading_times) + error_count)
                    if (len(loading_times) + error_count) > 0
                    else 0
                ),
            }

        return report
