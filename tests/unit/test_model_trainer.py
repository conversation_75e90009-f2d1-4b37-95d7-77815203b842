"""
Tests for the ModelTrainer class in src.train.trainer module.
"""

# pylint: disable=protected-access
# This is needed because we need to test protected methods

import json
from pathlib import Path
from unittest import mock
from unittest.mock import MagicMock

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.config.paths import get_run_paths
from src.models.validation import ModelValidationError
from src.train.callbacks.early_stopping import EarlyStoppingCallback
from src.train.trainer import ModelTrainer
from src.train.trainer_config import DatabaseConfig, TrainerConfig


class SimpleModel(nn.Module):
    """A simple model for testing."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture
def mock_data_loaders():
    """Create mock data loaders for training and testing."""
    # Create random data
    x_train = torch.randn(20, 10)
    y_train = torch.randint(0, 2, (20, 1)).float()
    x_test = torch.randn(10, 10)
    y_test = torch.randint(0, 2, (10, 1)).float()

    # Create datasets
    train_dataset = TensorDataset(x_train, y_train)
    test_dataset = TensorDataset(x_test, y_test)

    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=5)
    test_loader = DataLoader(test_dataset, batch_size=5)

    return {"train": train_loader, "test": test_loader}


@pytest.fixture
def model_components():
    """Create model components for testing."""
    model = SimpleModel()
    loss_fn = nn.BCEWithLogitsLoss()
    optimizer = optim.SGD(model.parameters(), lr=0.01)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture
def training_config():
    """Create training configuration for testing."""
    yield {
        "model_id": "test_model",
        "model_run_uuid": "test-model-run-uuid",
        "epochs": 2,
    }


# pylint: disable=redefined-outer-name
# This is needed because pytest fixtures are used as test function parameters
class TestModelTrainer:
    """Tests for the ModelTrainer class."""

    def test_initialization(self, model_components, mock_data_loaders, training_config):
        """Test that the ModelTrainer initializes correctly."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Check that the trainer has the expected attributes
        assert trainer.components.model == model_components["model"]
        assert trainer.components.loss_fn == model_components["loss_fn"]
        assert trainer.components.optimizer == model_components["optimizer"]
        assert trainer.data_loaders == mock_data_loaders
        assert trainer.config.config == training_config
        assert trainer.validation_result is None  # No architecture_params provided

        # Check that metrics are initialized correctly with the new nested structure
        assert hasattr(trainer.metrics.storage, "train")
        assert hasattr(trainer.metrics.storage, "test")
        assert hasattr(trainer.metrics.storage, "classification")
        assert hasattr(trainer.metrics.storage.train, "losses")
        assert hasattr(trainer.metrics.storage.train, "accuracies")
        assert hasattr(trainer.metrics.storage.test, "losses")
        assert hasattr(trainer.metrics.storage.test, "accuracies")
        assert hasattr(trainer.metrics.storage, "timing")
        assert hasattr(trainer.metrics.storage, "resources")
        assert hasattr(trainer.metrics, "custom_metrics")
        assert hasattr(trainer.metrics, "batch_metrics")

    def test_metrics_collection(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that metrics are collected correctly during training."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock the resource collection in the MetricsCollectionCallback
        # to avoid actual system calls
        metrics_callback = trainer.get_metrics_callback()
        with mock.patch.object(metrics_callback, "_collect_resource_metrics"):
            # Train the model
            trainer.train()

            # Check that metrics were collected using the new nested structure
            assert len(trainer.metrics.storage.train.losses) == training_config["epochs"]
            assert len(trainer.metrics.storage.test.losses) == training_config["epochs"]
            assert len(trainer.metrics.storage.train.accuracies) == training_config["epochs"]
            assert len(trainer.metrics.storage.test.accuracies) == training_config["epochs"]

            # Check timing metrics
            assert trainer.metrics.storage.timing["start_time"] is not None
            assert trainer.metrics.storage.timing["end_time"] is not None
            assert (
                len(trainer.metrics.storage.timing["epoch_times"]) == training_config["epochs"]
            )
            assert (
                len(trainer.metrics.storage.timing["train_times"]) == training_config["epochs"]
            )
            assert (
                len(trainer.metrics.storage.timing["validation_times"])
                == training_config["epochs"]
            )

            # Check that total training time is calculated
            assert "total_training_time" in trainer.metrics.storage.timing
            assert trainer.metrics.storage.timing["total_training_time"] > 0

            # Check that batch metrics were collected
            assert len(trainer.metrics.batch_metrics) > 0

            # Check structure of batch metrics
            batch_metric = trainer.metrics.batch_metrics[0]
            assert "epoch" in batch_metric
            assert "batch" in batch_metric
            assert "loss" in batch_metric
            assert "accuracy" in batch_metric
            assert "time" in batch_metric

    def test_custom_metrics(self, model_components, mock_data_loaders, training_config):
        """Test that custom metrics can be added."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Add custom metrics
        trainer.add_custom_metric("test_metric", 0.5)
        trainer.add_custom_metric("test_metric", 0.6)
        trainer.add_custom_metric("another_metric", 0.7)

        # Check that custom metrics were added
        assert "test_metric" in trainer.metrics.custom_metrics
        assert len(trainer.metrics.custom_metrics["test_metric"]) == 2
        assert trainer.metrics.custom_metrics["test_metric"] == [0.5, 0.6]

        assert "another_metric" in trainer.metrics.custom_metrics
        assert len(trainer.metrics.custom_metrics["another_metric"]) == 1
        assert trainer.metrics.custom_metrics["another_metric"] == [0.7]

    def test_get_metrics(self, model_components, mock_data_loaders, training_config):
        """Test that get_metrics returns a copy of the metrics."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Add a custom metric
        trainer.add_custom_metric("test_metric", 0.5)

        # Get metrics
        metrics = trainer.get_metrics()

        # Check that metrics is a copy
        assert metrics is not trainer.metrics

        # Modify the returned metrics
        metrics["custom_metrics"]["test_metric"].append(0.6)

        # Check that the original metrics were not modified
        assert len(trainer.metrics.custom_metrics["test_metric"]) == 1
        assert trainer.metrics.custom_metrics["test_metric"] == [0.5]

    def test_metrics_saved_to_file(
        self, model_components, mock_data_loaders, training_config, monkeypatch
    ):  # pylint: disable=too-many-locals
        """Test that metrics are saved to files correctly."""

        run_paths = get_run_paths(training_config["model_run_uuid"])
        run_output_dir = Path(run_paths.base)
        monkeypatch.setattr("src.config.paths.RUNS_BASE_DIR", run_output_dir.parent)

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock the resource collection in the MetricsCollectionCallback
        # to avoid actual system calls
        metrics_callback = trainer.get_metrics_callback()
        with mock.patch.object(metrics_callback, "_collect_resource_metrics"):
            # Train the model
            trainer.train()

            run_paths = get_run_paths(training_config["model_run_uuid"])
            metrics_path = run_paths.metrics_summary
            detailed_metrics_path = run_paths.metrics_history

            assert (
                metrics_path.exists()
            ), f"Metrics summary file not found at {metrics_path}"
            assert (
                detailed_metrics_path.exists()
            ), f"Metrics history file not found at {detailed_metrics_path}"

            # Load and check metrics
            with open(metrics_path, "r", encoding="utf-8") as f:
                metrics = json.load(f)

            assert "final" in metrics
            final = metrics["final"]
            assert "train_loss" in final
            assert "test_loss" in final
            assert "train_accuracy" in final
            assert "test_accuracy" in final
            assert "timing" in metrics
            assert "resources" in metrics

            # Load and check detailed metrics with the new nested structure
            with open(detailed_metrics_path, "r", encoding="utf-8") as f:
                detailed_metrics = json.load(f)

            assert "train" in detailed_metrics
            assert "test" in detailed_metrics
            assert "losses" in detailed_metrics["train"]
            assert "accuracies" in detailed_metrics["train"]
            assert "losses" in detailed_metrics["test"]
            assert "accuracies" in detailed_metrics["test"]
            assert "timing" in detailed_metrics
            assert "resources" in detailed_metrics
            assert "custom_metrics" in detailed_metrics

    def test_early_stopping_callback(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that the EarlyStoppingCallback stops training early."""
        # Use a higher epoch count to allow for early stopping
        training_config["epochs"] = 10
        patience = 2

        # Initialize the callback
        early_stopping_callback = EarlyStoppingCallback(
            monitor="validation_loss", patience=patience, verbose=False
        )

        # Initialize the trainer with the callback
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
            callbacks=[early_stopping_callback],
        )
        trainer = ModelTrainer(config)

        # Mock the validation epoch to simulate no improvement
        # The first call should establish a baseline, subsequent calls should be worse.
        validation_losses = [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]
        side_effects = [
            {"validation_loss": loss, "validation_accuracy": 0.5}
            for loss in validation_losses
        ]

        with mock.patch.object(
            trainer, "_validate_epoch", side_effect=side_effects
        ) as mock_validate:
            # Mock the training epoch to return some dummy metrics
            with mock.patch.object(
                trainer,
                "_train_epoch",
                return_value={"train_loss": 0.5, "train_accuracy": 0.5},
            ):
                # Mock resource collection
                metrics_callback = trainer.get_metrics_callback()
                with mock.patch.object(metrics_callback, "_collect_resource_metrics"):
                    trainer.train()

                    # The first epoch sets the baseline.
                    # The next `patience` epochs will not improve.
                    # Training should stop after `1 + patience` epochs.
                    expected_epochs = 1 + patience
                    assert len(trainer.metrics.storage.train.losses) == expected_epochs
                    assert mock_validate.call_count == expected_epochs
                    assert trainer.stop_training is True

    def test_database_config_integration(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that DatabaseConfig is properly integrated."""

        # Test with database config
        database_config = DatabaseConfig(
            model_run_uuid="test-uuid-123", profile="development"
        )

        # Mock the ModelRunCallback import to avoid actual database dependencies
        with mock.patch("src.train.trainer.ModelRunCallback") as mock_callback_class:
            mock_callback_instance = MagicMock()
            mock_callback_class.return_value = mock_callback_instance

            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=training_config,
                database_config=database_config,
            )
            trainer = ModelTrainer(config)

            # Verify that ModelRunCallback was created with correct parameters
            mock_callback_class.assert_called_once_with(
                model_run_uuid="test-uuid-123",
                profile="development",
                verbose=True,
            )

            # Verify that the callback was added to the handler
            assert mock_callback_instance in trainer.callback_handler.callbacks

    def test_database_config_none(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that trainer works without database config."""
        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
            database_config=None,
        )
        trainer = ModelTrainer(config)

        # Should have MetricsCollectionCallback, TestImageVisualizationCallback,
        # and MetricsPlottingCallback
        assert len(trainer.callback_handler.callbacks) == 3
        callback_names = [
            cb.__class__.__name__ for cb in trainer.callback_handler.callbacks
        ]
        assert "MetricsCollectionCallback" in callback_names
        assert "TestImageVisualizationCallback" in callback_names

    def test_init_loss_function_from_model_components(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that loss function is initialized correctly from model_components."""
        # Create a custom loss function to be passed in model_components
        custom_loss = nn.BCEWithLogitsLoss(reduction="sum")
        model_components["loss_fn"] = custom_loss

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # The loss function should be the one from model_components
        loss_fn = trainer._init_loss_function(config)
        assert loss_fn is custom_loss

    def test_init_loss_function_from_structured_config(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that loss function is initialized correctly from structured config."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add structured loss function config to training_config
        training_config["model_version_parameters"] = {
            "loss_function": {
                "type": "bce",
                "config": {"reduction": "sum", "pos_weight": torch.tensor([2.0])},
            }
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Initialize the loss function
        with mock.patch("src.train.trainer.create_loss_function") as mock_create_loss:
            trainer._init_loss_function(config)

            # Verify that create_loss_function was called with the correct parameters
            mock_create_loss.assert_called_once_with(
                {
                    "type": "bce",
                    "config": {"reduction": "sum", "pos_weight": torch.tensor([2.0])},
                }
            )

    def test_init_loss_function_with_cross_entropy(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that loss function is initialized correctly with CrossEntropy loss."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add structured loss function config to training_config with CrossEntropy
        training_config["model_version_parameters"] = {
            "loss_function": {
                "type": "ce",
                "config": {
                    "reduction": "mean",
                    "label_smoothing": 0.1,
                    "ignore_index": -100,
                },
            }
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Initialize the loss function
        with mock.patch("src.train.trainer.create_loss_function") as mock_create_loss:
            trainer._init_loss_function(config)

            # Verify that create_loss_function was called with the correct parameters
            mock_create_loss.assert_called_once_with(
                {
                    "type": "ce",
                    "config": {
                        "reduction": "mean",
                        "label_smoothing": 0.1,
                        "ignore_index": -100,
                    },
                }
            )

    def test_init_loss_function_with_invalid_config(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that loss function initialization handles invalid config gracefully."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add invalid loss function config to training_config (missing 'type' key)
        training_config["model_version_parameters"] = {
            "loss_function": {"config": {"reduction": "mean"}}
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock logger to check warning message
        with mock.patch.object(trainer.logger, "warning") as mock_warning:
            # Initialize the loss function
            loss_fn = trainer._init_loss_function(config)

            # Verify that a warning was logged
            mock_warning.assert_called_once()
            assert (
                "Invalid loss function configuration format"
                in mock_warning.call_args[0][0]
            )

            # Verify that a default loss function was created
            assert loss_fn is not None

    def test_init_loss_function_with_exception(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that loss function initialization handles exceptions gracefully."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # Add valid loss function config to training_config
        training_config["model_version_parameters"] = {
            "loss_function": {"type": "bce", "config": {"reduction": "mean"}}
        }

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Create a mock that raises an exception only on the first call
        mock_create_loss = MagicMock()
        mock_create_loss.side_effect = [
            ValueError("Test error"),  # First call raises an exception
            nn.BCEWithLogitsLoss(),  # Second call returns a default loss function
        ]

        # Mock create_loss_function to use our controlled mock
        with mock.patch("src.train.trainer.create_loss_function", mock_create_loss):
            # Mock logger to check error message
            with mock.patch.object(trainer.logger, "error") as mock_error:
                # Initialize the loss function
                loss_fn = trainer._init_loss_function(config)

                # Verify that an error was logged
                mock_error.assert_called_once()
                assert "Error creating loss function" in mock_error.call_args[0][0]
                assert "Test error" in mock_error.call_args[0][1]

                # Verify that a default loss function was created
                assert loss_fn is not None

                # Verify that create_loss_function was called twice
                assert mock_create_loss.call_count == 2

    def test_init_loss_function_default_fallback(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test loss function init falls back to default when no config is provided."""
        # Remove any loss function from model_components
        if "loss_fn" in model_components:
            del model_components["loss_fn"]

        # No loss function config in training_config
        if "model_version_parameters" in training_config:
            del training_config["model_version_parameters"]

        config = TrainerConfig(
            model_components=model_components,
            data_loaders=mock_data_loaders,
            training_config=training_config,
        )
        trainer = ModelTrainer(config)

        # Mock create_loss_function to verify default parameters
        with mock.patch("src.train.trainer.create_loss_function") as mock_create_loss:
            # Initialize the loss function
            trainer._init_loss_function(config)

            # Verify that create_loss_function was called with default parameters
            mock_create_loss.assert_called_once_with(
                {"type": "bce", "config": {"reduction": "mean"}}
            )

            # Mock logger to check info message
            with mock.patch.object(trainer.logger, "info") as mock_info:
                trainer._init_loss_function(config)

                # Verify that an info message was logged
                assert any(
                    "Using default BCE loss function" in call[0][0]
                    for call in mock_info.call_args_list
                )

    def test_model_validation_with_architecture_params(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that model validation is performed when architecture_params are provided."""
        architecture_params = {
            "name": "CNN",
            "parameters": {
                "model_version": {"parameters": {"num_classes": 2}},
                "model_run": {"uuid": "test-uuid"},
            },
        }

        # Mock the ModelValidator to return successful validation
        with mock.patch(
            "src.train.trainer.ModelValidator.validate_model_dataset_compatibility"
        ) as mock_validate:
            mock_validate.return_value = {
                "is_compatible": True,
                "dataset_num_classes": 2,
                "model_output_size": 1,
                "label_info": {"num_classes": 2},
                "architecture_params": architecture_params,
            }

            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=training_config,
                architecture_params=architecture_params,
            )
            trainer = ModelTrainer(config)

            # Verify that validation was called
            mock_validate.assert_called_once_with(
                model=model_components["model"],
                data_loaders=mock_data_loaders,
                architecture_params=architecture_params,
            )

            # Verify that validation result is stored
            assert trainer.validation_result is not None
            assert trainer.validation_result["is_compatible"] is True
            assert trainer.validation_result["dataset_num_classes"] == 2

    def test_model_validation_failure(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that ModelValidationError is raised when validation fails."""
        architecture_params = {
            "name": "CNN",
            "parameters": {
                "model_version": {"parameters": {"num_classes": 2}},
                "model_run": {"uuid": "test-uuid"},
            },
        }

        # Mock the ModelValidator to raise validation error
        with mock.patch(
            "src.train.trainer.ModelValidator.validate_model_dataset_compatibility"
        ) as mock_validate:
            mock_validate.side_effect = ModelValidationError(
                "Model output size mismatch"
            )

            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=training_config,
                architecture_params=architecture_params,
            )

            # Verify that ModelValidationError is raised
            with pytest.raises(
                ModelValidationError, match="Model output size mismatch"
            ):
                ModelTrainer(config)

    def test_model_validation_skipped_without_architecture_params(
        self, model_components, mock_data_loaders, training_config
    ):
        """Test that model validation is skipped when no architecture_params are provided."""
        with mock.patch(
            "src.train.trainer.ModelValidator.validate_model_dataset_compatibility"
        ) as mock_validate:
            config = TrainerConfig(
                model_components=model_components,
                data_loaders=mock_data_loaders,
                training_config=training_config,
                architecture_params=None,
            )
            trainer = ModelTrainer(config)

            # Verify that validation was not called
            mock_validate.assert_not_called()

            # Verify that validation result is None
            assert trainer.validation_result is None
