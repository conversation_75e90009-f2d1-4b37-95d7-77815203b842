"""
Manual test script for the dynamic CNN training pipeline with concurrent job execution.

This script tests the TrainingJobDispatcher's concurrency capabilities by running
two training jobs simultaneously:
1. Training with augmentations (horizontal flip, rotation, zoom)
2. Training without augmentations (same network architecture and data)

Features tested:
- Concurrent job execution using TrainingJobDispatcher
- Resource management and job queuing
- Progress monitoring with detailed status updates
- Model creation using MLModelFactory
- Data loading with augmentation support
- Training loop using ModelTrainer
- Saving of model checkpoints, metrics, and plots

Production Resource Management Recommendations:
1. Set reasonable resource limits based on system capacity
2. Implement job priority queuing for critical vs. experimental runs
3. Add graceful degradation (reduce batch size when resources are low)
4. Use cloud auto-scaling for handling peak training loads
5. Monitor resource usage and set up alerting
6. Consider distributed training for large models

Run this script from the root of the project:
```
python tests/manual/test_dynamic_cnn_pipeline.py
```
"""

import asyncio
import copy
import os
import sys
import time

import torch

# This is a hack to get the tests to run from the command line
# It modifies sys.path to include the project root, allowing src imports.
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
sys.path.insert(0, project_root)

from src.augmentations import (  # noqa: E402
    AugmentationPipelineFactory,
    AugmentationUtils,
)
from src.config.paths import get_run_paths  # noqa: E402
from src.database.services.training_data_service import (  # noqa: E402
    TrainingDataService,
)
from src.jobs.base import JobStatus  # noqa: E402
from src.jobs.training_data import TrainingJob, TrainingJobData  # noqa: E402
from src.jobs.training_dispatcher import (  # noqa: E402
    DispatcherConfig,
    TrainingJobDispatcher,
)
from src.models import MLModelFactory  # noqa: E402
from src.train.callbacks.model_checkpoint import ModelCheckpoint  # noqa: E402
from src.train.trainer import ModelTrainer, TrainerConfig  # noqa: E402
from tests.manual.test_data_utils import (  # noqa: E402
    augmented_data_loader,
    generate_gaussian_blurs,
)

# Plots are now handled automatically by training callbacks

# Note: Classification metrics (precision, recall, F1-score) are now automatically
# calculated and stored by the trainer during validation, eliminating the need
# for a custom ClassificationMetricsCallback

# Note: Artifact management (models, metrics, augmentations, labels) is now handled
# automatically by the integrated TrainingArtifactManager within the trainer


# Note: FeatureMapVisualizationCallback is now automatically added by the trainer
# when the model supports feature extraction (CNN models with Conv2d layers)


# Create Gaussian blurs with different widths
NUMBER_PER_CLASS = 1000
IMAGE_SIZE = 91

# Generate training data
images, labels = generate_gaussian_blurs(
    image_size=IMAGE_SIZE, number_per_class=NUMBER_PER_CLASS
)


# Use existing utility to extract augmentations from training_data
def extract_augmentations_from_training_data(training_data):
    """Extract and create augmentation objects from training_data using existing utilities."""
    augmentation_configs = training_data["model_run"].get("augmentations", [])
    # Use the existing utility function to convert dict list to augmentation objects
    return AugmentationUtils.from_dict_list(augmentation_configs)


# Custom TrainingJobDispatcher for test scenarios
class ManualTestTrainingJobDispatcher(TrainingJobDispatcher):
    """Custom dispatcher that works with test data instead of database."""

    def __init__(self, test_images, test_labels, max_concurrent_jobs=2):
        # Initialize with no auto-start and no database polling
        config = DispatcherConfig(
            auto_start=False,
            polling_interval=999999,  # Effectively disable polling
            profile=None,
            max_concurrent_jobs=max_concurrent_jobs,  # Allow concurrent jobs
            # For production, consider these resource management strategies:
            # 1. Set reasonable limits based on your system capacity
            # 2. Use job queuing with priority levels
            # 3. Implement graceful degradation (reduce batch size, etc.)
            # 4. Add resource monitoring and alerting
            # 5. Consider cloud auto-scaling for peak loads
            # For testing, we disable limits to ensure jobs run
            max_cpu_usage_percent=100.0,
            max_memory_usage_percent=100.0,
            max_gpu_memory_usage_percent=100.0,
        )
        super().__init__(config=config, callbacks=[])
        self.test_images = test_images
        self.test_labels = test_labels
        self.training_data_cache = {}  # Cache for different training data configs
        self.architecture_params_cache = {}  # Cache for architecture params
        self._disable_polling = True  # Flag to disable polling completely

    async def schedule_job(self, job) -> None:
        """Override to fix type checking and add debug information."""
        from src.jobs.training_data import TrainingJob as ExpectedTrainingJob

        print(f"Scheduling job type: {type(job)}")
        print(f"Job is TrainingJob: {isinstance(job, TrainingJob)}")
        print(f"Job is ExpectedTrainingJob: {isinstance(job, ExpectedTrainingJob)}")
        print(f"TrainingJob module: {TrainingJob.__module__}")
        print(f"ExpectedTrainingJob module: {ExpectedTrainingJob.__module__}")

        # Ensure we're using the correct type
        if not isinstance(job, (TrainingJob, ExpectedTrainingJob)):
            raise ValueError(
                f"Only TrainingJob instances can be scheduled, got {type(job)}"
            )

        job.status = JobStatus.SCHEDULED
        self.jobs[job.job_id] = job

        # Notify callbacks
        self._notify_callbacks("on_job_scheduled", job)

        # Add job to queue
        await self.job_queue.put(job)

    async def _prepare_augmented_data_loaders(
        self, training_data, job
    ):  # pylint: disable=unused-argument
        """Override to use test data instead of database data."""
        # Extract augmentations from training data
        augmentations = AugmentationUtils.from_dict_list(
            training_data["model_run"].get("augmentations", [])
        )

        # Create augmentation pipelines
        train_transforms = AugmentationPipelineFactory.create_training_pipeline(
            augmentations
        )
        val_transforms = AugmentationPipelineFactory.create_inference_pipeline(
            augmentations
        )

        # Get batch size from training parameters
        batch_size = training_data["training_parameters"].get("batch_size", 32)

        # Create data loaders using test data
        # The enhanced TestImageVisualizationCallback will automatically detect augmentations
        train_loader, test_loader = augmented_data_loader(
            self.test_images,
            self.test_labels,
            transforms={"train": train_transforms, "val": val_transforms},
            config={"batch_size": batch_size, "test_size": 0.1},
        )

        # Build result with augmentation metadata
        return {
            "train": train_loader,
            "test": test_loader,
            "_augmentation_metadata": {
                "augmentations": augmentations,
                "train_transforms": train_transforms,
                "val_transforms": val_transforms,
                "augmentation_count": len(augmentations),
            },
        }

    def set_training_data(self, model_run_uuid, training_data):
        """Set training data for a specific model run UUID."""
        self.training_data_cache[model_run_uuid] = training_data

    def set_architecture_params(self, model_run_uuid, architecture_params):
        """Set architecture params for a specific model run UUID."""
        self.architecture_params_cache[model_run_uuid] = architecture_params

    async def _run_training(self, job):
        """Override to use test training data."""
        # Get the training data for this specific job
        model_run_uuid = job.data.model_run_uuid
        training_data = self.training_data_cache.get(model_run_uuid)

        if training_data is None:
            raise ValueError(
                f"No training data found for model run UUID: {model_run_uuid}"
            )

        # Prepare data loaders with augmentation support
        data_loaders = await self._prepare_augmented_data_loaders(training_data, job)

        # Get architecture params for this model run
        architecture_params = self.architecture_params_cache.get(model_run_uuid)

        # Create trainer config with augmented data loaders
        trainer_config = TrainerConfig(
            model_components=job.data.model_components,
            data_loaders=data_loaders,
            training_config=job.data.training_config,
            callbacks=job.data.callbacks,
            database_config=None,  # No database integration for test
            architecture_params=architecture_params,
        )

        # Create and run trainer
        trainer = ModelTrainer(trainer_config)

        # Run training synchronously for test
        trainer.train()

        return trainer

    async def _poll_for_jobs(self) -> None:
        """Override to disable database polling for test scenarios."""
        # Do nothing - we don't want to poll the database in tests
        pass

    def _check_resources(self, job) -> bool:
        # For testing, be very permissive - just check basic availability
        try:
            import psutil

            memory = psutil.virtual_memory()
            available_memory_gb = memory.available / (1024**3)

            # Very basic check - just ensure we have at least 1GB available
            if available_memory_gb < 1.0:
                print(f"❌ Very low memory: {available_memory_gb:.1f}GB available")
                return False

            # Print resource status for debugging
            cpu_percent = psutil.cpu_percent(interval=0.1)
            print(f"✅ Resource check passed for job {job.job_id}:")
            print(f"   CPU: {cpu_percent:.1f}% usage")
            print(f"   Memory: {available_memory_gb:.1f}GB available")
            return True

        except Exception as e:
            print(f"Error in resource checking: {e}")
            # Fall back to allowing the job
            return True

    async def _execute_job(self, job):
        """Override to add better error handling and ensure proper cleanup."""
        try:
            print(f"Starting execution of job: {job.job_id}")

            # Mark job as running
            job.status = JobStatus.RUNNING
            job.started_at = asyncio.get_event_loop().time()

            # Notify callbacks
            self._notify_callbacks("on_job_start", job)

            # Execute the training job
            result = await self._run_training(job)

            # Mark job as completed
            job.status = JobStatus.COMPLETED
            job.completed_at = asyncio.get_event_loop().time()
            job.result = result

            # Move job to completed jobs
            self.completed_jobs[job.job_id] = job

            # Notify callbacks
            self._notify_callbacks("on_job_complete", job)

            print(f"Job {job.job_id} completed successfully")
            return result

        except Exception as e:
            print(f"Job {job.job_id} failed with error: {e}")

            # Mark job as failed
            job.status = JobStatus.FAILED
            job.completed_at = asyncio.get_event_loop().time()
            job.error = e

            # Move job to completed jobs (even if failed)
            self.completed_jobs[job.job_id] = job

            # Notify callbacks
            self._notify_callbacks("on_job_error", job, e)

            # Don't re-raise the exception to prevent the dispatcher from stopping
            return None
        finally:
            # Always remove from running jobs
            if job.job_id in self.running_jobs:
                del self.running_jobs[job.job_id]


# Data loading will be done after augmentation pipeline creation

# Record start time
start_time = time.process_time()

# Create training_data structure that mimics exactly what comes from the database
# This structure matches what TrainingDataService.get_training_data() returns
training_data = {
    "model": {
        "uuid": "test-model-uuid",
        "name": "Test CNN Model",
        "description": "Test model for dynamic CNN pipeline",
        "architecture": "CNN",
        "user_id": "test-user-id",
        "created_at": "2024-01-01T00:00:00Z",
    },
    "model_version": {
        "uuid": "test-model-version-uuid",
        "model_uuid": "test-model-uuid",
        "version_number": 1,
        "parameters": {
            # Model architecture parameters (used by MLModelFactory)
            "convolutional_layers": [[16, 3, 1, 1], [32, 3, 1, 1]],
            "fully_connected_layers": [64],
            "activation": "relu",
            "batch_norm": True,
            "pooling": ["max", 2, 2],
            "loss_function": {"type": "BCEWithLogitsLoss"},  # Loss function config
        },
        "created_at": "2024-01-01T00:00:00Z",
        "user_id": "test-user-id",
        "name": "Test Model Version",
        "note": "Test version for dynamic CNN pipeline",
    },
    "model_run": {
        "uuid": "test-model-run-uuid-augmentations",
        "model_version_uuid": "test-model-version-uuid",
        "dataset_uuid": "test-dataset-uuid",
        "status": "scheduled",
        "parameters": {
            # Training-specific parameters
            "dropout_rate": 0.3,
            "learning_rate": 0.001,
            "batch_size": 32,
            "epochs": 10,
        },
        # Augmentations as they would be stored in the database model_run
        "augmentations": [
            # {"resize_dimensions": [IMAGE_SIZE, IMAGE_SIZE]},
            {"horizontal_flip": True},
            {"rotation_range": 15.0},
            {"zoom_range": 0.2},
        ],
        "created_at": "2024-01-01T00:00:00Z",
        "is_training": True,
        "is_testing": False,
    },
    "dataset": {
        "uuid": "test-dataset-uuid",
        "name": "Test Gaussian Blurs Dataset",
        "description": "Generated Gaussian blurs for testing",
        "images_count": NUMBER_PER_CLASS * 2,
        "created_at": "2024-01-01T00:00:00Z",
        "content_updated_at": "2024-01-01T00:00:00Z",
    },
    # Placeholder for training_parameters - will be set after training_data is defined
    "training_parameters": {},
    "dataset_metadata": {
        "name": "Test Gaussian Blurs Dataset",
        "description": "Generated Gaussian blurs for testing",
        "images_count": NUMBER_PER_CLASS * 2,
        "created_at": "2024-01-01T00:00:00Z",
        "content_updated_at": "2024-01-01T00:00:00Z",
    },
}

# Additional metadata for the training pipeline
training_metadata = {
    "image_size": IMAGE_SIZE,
    "image_channels": 1,  # Will be updated automatically based on augmentations
    "num_classes": 1,  # Binary classification
}

# Set training_parameters using TrainingDataService.combine_training_parameters
training_data["training_parameters"] = TrainingDataService.combine_training_parameters(
    model_version_data=training_data.get("model_version", {}),
    model_run_data=training_data.get("model_run", {}),
)

# Extract augmentations from training_data (simulating database-driven configuration)
print("Extracting augmentations from training_data...")
augmentations = extract_augmentations_from_training_data(training_data)
print(
    f"Found {len(augmentations)} augmentations: {[type(aug).__name__ for aug in augmentations]}"
)

# Update training metadata with correct channels based on augmentations
# For now, keep the original channels since current augmentations don't change channel requirements
original_channels = training_metadata.get("image_channels", 1)
training_metadata["image_channels"] = original_channels

# Create second training data configuration (clone and modify)
training_data_no_augmentations = copy.deepcopy(training_data)
training_data_no_augmentations["model_run"]["uuid"] = "test-model-run-uuid"
training_data_no_augmentations["model_run"].pop(
    "augmentations", None
)  # Remove augmentations

print("Created two training configurations:")
print(f"1. With augmentations: {training_data['model_run']['uuid']}")
print(f"   Augmentations: {len(training_data['model_run'].get('augmentations', []))}")
print(
    f"2. Without augmentations: {training_data_no_augmentations['model_run']['uuid']}"
)
print(
    f"   Augmentations: {len(training_data_no_augmentations['model_run'].get('augmentations', []))}"
)

# Now use the TrainingJobDispatcher to handle the training flow properly
print("Setting up TrainingJobDispatcher for concurrent training...")

# Create the test dispatcher with our data (supports 2 concurrent jobs)
dispatcher = ManualTestTrainingJobDispatcher(images, labels, max_concurrent_jobs=2)

# Set training data for both configurations
dispatcher.set_training_data(training_data["model_run"]["uuid"], training_data)
dispatcher.set_training_data(
    training_data_no_augmentations["model_run"]["uuid"], training_data_no_augmentations
)


def create_model_components_for_training_data(training_data_config, training_metadata):
    """Create model components for a specific training data configuration."""
    model_factory = MLModelFactory()
    num_classes = training_metadata["num_classes"]

    # Update training_parameters for this configuration
    training_data_config["training_parameters"] = (
        TrainingDataService.combine_training_parameters(
            model_version_data=training_data_config.get("model_version", {}),
            model_run_data=training_data_config.get("model_run", {}),
        )
    )

    # Create architecture_params structure for MLModelFactory
    architecture_params = {
        "name": training_data_config["model"]["architecture"],
        "parameters": {
            "model_version": {
                "parameters": {
                    # Extract the model architecture parameters
                    "convolutional_layers": training_data_config["training_parameters"][
                        "convolutional_layers"
                    ],
                    "fully_connected_layers": training_data_config[
                        "training_parameters"
                    ]["fully_connected_layers"],
                    "activation": training_data_config["training_parameters"][
                        "activation"
                    ],
                    "batch_norm": training_data_config["training_parameters"][
                        "batch_norm"
                    ],
                    "pooling": training_data_config["training_parameters"]["pooling"],
                }
            },
            "model_run": {
                "uuid": training_data_config["model_run"]["uuid"],
                "parameters": {
                    "dropout_rate": training_data_config["training_parameters"][
                        "dropout_rate"
                    ],
                },
            },
            "image_size": training_metadata["image_size"],
            "image_channels": training_metadata["image_channels"],
        },
    }

    # Create model components
    network = model_factory.create_model(
        architecture_params=architecture_params, num_classes=num_classes
    )

    loss_fn = model_factory.create_loss_function_from_architecture(
        architecture_params=architecture_params, num_classes=num_classes
    )

    optimizer_config = {"type": "adam", "learning_rate": 0.001}
    optimizer = model_factory.create_optimizer(
        optimizer_params=optimizer_config, model_parameters=network.parameters()
    )

    # Initialize the model with better weight initialization
    for m in network.modules():
        if isinstance(m, torch.nn.Conv2d):
            torch.nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
        elif isinstance(m, torch.nn.Linear):
            torch.nn.init.xavier_normal_(m.weight)
            if m.bias is not None:
                torch.nn.init.constant_(m.bias, 0)

    return {
        "model": network,
        "loss_fn": loss_fn,
        "optimizer": optimizer,
    }, architecture_params


# Create model components for both training configurations
print("Creating model components for both configurations...")
model_components_1, architecture_params_1 = create_model_components_for_training_data(
    training_data, training_metadata
)
model_components_2, architecture_params_2 = create_model_components_for_training_data(
    training_data_no_augmentations, training_metadata
)

print("Created models for both configurations:")
print(f"1. With augmentations: {type(model_components_1['model']).__name__}")
print(f"2. Without augmentations: {type(model_components_2['model']).__name__}")

# Set architecture params for both configurations
dispatcher.set_architecture_params(
    training_data["model_run"]["uuid"], architecture_params_1
)
dispatcher.set_architecture_params(
    training_data_no_augmentations["model_run"]["uuid"], architecture_params_2
)

# Verify pipeline setup
augmentations = extract_augmentations_from_training_data(training_data)
print(f"Pipeline configured for {len(augmentations)} augmentations")
print(
    f"Data shape: {images.shape}, Model expects: "
    f"{training_metadata['image_channels']} channels"
)


def create_training_job(model_run_uuid, model_components):
    """Create a training job for a specific configuration."""
    # Prepare training configuration for the job (reduced epochs for faster testing)
    job_training_config = {
        "model_id": model_run_uuid,
        "model_run_uuid": model_run_uuid,
        "epochs": 10,
    }

    # Create callbacks that will be used by the trainer
    checkpoint_callback = ModelCheckpoint(
        model_run_uuid=model_run_uuid,
        monitor="validation_loss",
        verbose=True,
        mode="min",
    )

    # Create the training job data
    job_data = TrainingJobData(
        model_run_uuid=model_run_uuid,
        profile=None,  # No database profile for test
        model_components=model_components,
        data_loaders={},  # Will be created by dispatcher
        training_config=job_training_config,
        callbacks=[
            checkpoint_callback,
            # TestImageVisualizationCallback will be automatically added by the trainer
            # FeatureMapVisualizationCallback will be automatically added by the trainer
            # Classification metrics are now automatically calculated by the trainer
        ],
    )

    # Create the training job
    return TrainingJob(job_id=f"training-{model_run_uuid}", data=job_data, priority=100)


# Create training jobs for both configurations
print("Creating training jobs for both configurations...")
job_1 = create_training_job(training_data["model_run"]["uuid"], model_components_1)
job_2 = create_training_job(
    training_data_no_augmentations["model_run"]["uuid"], model_components_2
)

print(f"Job 1 type: {type(job_1)}")
print(f"Job 2 type: {type(job_2)}")
print(f"Job 1 is TrainingJob: {isinstance(job_1, TrainingJob)}")
print(f"Job 2 is TrainingJob: {isinstance(job_2, TrainingJob)}")

print("Starting concurrent training using TrainingJobDispatcher...")


async def run_concurrent_training():
    """Run both training jobs concurrently with progress monitoring."""
    import time

    # Start the dispatcher
    await dispatcher.start()

    # Schedule jobs (can be 1 or more - dispatcher will handle dynamically)
    await dispatcher.schedule_job(job_1)
    await dispatcher.schedule_job(job_2)

    print("Both jobs scheduled. Monitoring progress...")
    print("Job 1 (with augmentations):", training_data["model_run"]["uuid"])
    print(
        "Job 2 (without augmentations):",
        training_data_no_augmentations["model_run"]["uuid"],
    )
    print("-" * 60)

    start_time = time.time()
    last_update = start_time

    # Wait for all jobs to complete (realistic approach - no need to know count upfront)
    # Keep running while there are jobs in queue or currently running
    while True:
        await asyncio.sleep(2)  # Check every 2 seconds

        current_time = time.time()
        elapsed = current_time - start_time

        running = len(dispatcher.running_jobs)
        completed = len(dispatcher.completed_jobs)
        queued = dispatcher.job_queue.qsize()

        # Print progress every 5 seconds or when status changes
        if current_time - last_update >= 5:
            print(
                f"[{elapsed:.1f}s] {completed} completed, {running} running, {queued} queued"
            )

            # Show which jobs are running
            if dispatcher.running_jobs:
                print("  Currently running:")
                for job_id in dispatcher.running_jobs.keys():
                    job_uuid = job_id.replace("training-", "")
                    job_type = (
                        "with augmentations"
                        if "augmentations" in job_uuid
                        else "without augmentations"
                    )
                    print(f"    - {job_uuid} ({job_type})")

            # Show completed jobs
            if dispatcher.completed_jobs:
                print("  Completed:")
                for job_id, job in dispatcher.completed_jobs.items():
                    job_uuid = job_id.replace("training-", "")
                    job_type = (
                        "with augmentations"
                        if "augmentations" in job_uuid
                        else "without augmentations"
                    )
                    status = "✓ Success" if job.result else "✗ Failed"
                    print(f"    - {job_uuid} ({job_type}) {status}")

            print("-" * 60)
            last_update = current_time

        # Check if all work is done (no jobs running or queued)
        if running == 0 and queued == 0 and completed > 0:
            break

        # Safety timeout after 5 minutes
        if elapsed > 300:
            print(f"\n⚠️ Timeout after {elapsed:.1f} seconds")
            break

    total_time = time.time() - start_time
    print(f"\n🎉 All training jobs completed in {total_time:.1f} seconds!")

    # Stop the dispatcher
    await dispatcher.stop()

    return dispatcher.completed_jobs


# Execute the concurrent training
completed_jobs = asyncio.run(run_concurrent_training())

end_time = 1000 * (time.process_time() - start_time)
print(f"Total execution time: {end_time:.2f} ms")

# Display results for both jobs
for job_id, job in completed_jobs.items():
    model_run_uuid = job.data.model_run_uuid
    trainer = job.result

    print(f"\n=== Results for {job_id} (UUID: {model_run_uuid}) ===")

    if trainer and hasattr(trainer, "metrics"):
        metrics = trainer.metrics

        # Access metrics using the new nested structure
        train_loss = metrics.train.losses
        test_loss = metrics.test.losses
        train_accuracy = metrics.train.accuracies
        test_accuracy = metrics.test.accuracies

        print(f"Final training loss: {train_loss[-1]:.4f}")
        print(f"Final test loss: {test_loss[-1]:.4f}")
        print(f"Final training accuracy: {train_accuracy[-1]:.4f}")
        print(f"Final test accuracy: {test_accuracy[-1]:.4f}")

        # Verify that classification metrics are automatically calculated and stored
        if hasattr(metrics, "classification") and metrics.classification:
            precision = metrics.classification.precision
            recall = metrics.classification.recall
            f1_score = metrics.classification.f1_score

            print("Classification metrics automatically calculated:")
            print(f"  Precision: {precision[-1]:.4f}")
            print(f"  Recall: {recall[-1]:.4f}")
            print(f"  F1-score: {f1_score[-1]:.4f}")

        # Show where plots and artifacts are saved
        run_paths = get_run_paths(model_run_uuid)
        run_dir = run_paths.base
        plots_dir = run_paths.plots
        print(f"Plots and artifacts saved to: {run_dir}")
        print(f"- Plots: {plots_dir}")
    else:
        print("No trainer result available")

print("\n=== Concurrency Test Summary ===")
print(f"Successfully ran {len(completed_jobs)} training job(s)")
if len(completed_jobs) >= 2:
    print(f"Job 1 (with augmentations): {training_data['model_run']['uuid']}")
    print(
        f"Job 2 (without augmentations): {training_data_no_augmentations['model_run']['uuid']}"
    )
    print(
        "Jobs completed successfully, demonstrating TrainingJobDispatcher concurrency capabilities"
    )
else:
    print(f"Job 1 (with augmentations): {training_data['model_run']['uuid']}")
    print(
        "Single job completed successfully, demonstrating TrainingJobDispatcher capabilities"
    )
