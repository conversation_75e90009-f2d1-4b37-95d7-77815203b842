"""
Integration tests for ModelTrainer with utility modules.

These tests focus on the integration between ModelTrainer and:
- src.utils.device (select_device)
- src.utils.logging (setup_training_logger)

The tests verify that utility functions work correctly with the trainer.
"""

# pylint: disable=duplicate-code

import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
import torch
from torch import nn, optim
from torch.utils.data import DataLoader, TensorDataset

from src.train.trainer import ModelTrainer, TrainerConfig
from tests.integration.trainer_test_utils import mock_training_and_run


class SimpleUtilsModel(nn.Module):
    """A simple model for testing utils integration."""

    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(3, 1)

    def forward(self, x):
        """Forward pass of the model."""
        return self.linear(x)


@pytest.fixture(name="utils_test_components")
def fixture_utils_test_components():
    """Create components for utils integration testing."""
    torch.manual_seed(42)
    model = SimpleUtilsModel()
    loss_fn = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    return {"model": model, "loss_fn": loss_fn, "optimizer": optimizer}


@pytest.fixture(name="utils_test_loaders")
def fixture_utils_test_loaders():
    """Create data loaders for utils integration testing."""
    torch.manual_seed(42)
    x_train = torch.randn(12, 3)
    y_train = torch.randn(12, 1)
    x_test = torch.randn(6, 3)
    y_test = torch.randn(6, 1)

    train_dataset = TensorDataset(x_train, y_train)
    test_dataset = TensorDataset(x_test, y_test)

    train_loader = DataLoader(train_dataset, batch_size=3)
    test_loader = DataLoader(test_dataset, batch_size=3)

    return {"train": train_loader, "test": test_loader}


class TestDeviceUtilsIntegration:
    """Integration tests for device selection utilities with ModelTrainer."""

    def test_device_selection_cpu_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test device selection integration when CPU is selected."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_cpu_device",
                "model_run_uuid": "test-cpu-device-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock select_device to return CPU
            with patch("src.train.trainer.select_device") as mock_select_device:
                cpu_device = torch.device("cpu")
                mock_select_device.return_value = cpu_device

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Verify device selection was called
                mock_select_device.assert_called_once()

                # Verify trainer uses the selected device
                assert trainer.device == cpu_device

                # Verify model was moved to the correct device
                model_device = next(trainer.components.model.parameters()).device
                assert model_device == cpu_device

                # Verify training works with the selected device (mocked for speed)
                metrics_callback = trainer.get_metrics_callback()
                with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
                    trainer, "_validate_epoch"
                ) as mock_validate, patch.object(
                    metrics_callback, "_collect_resource_metrics"
                ):
                    mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
                    mock_validate.return_value = {
                        "validation_loss": 0.6,
                        "validation_accuracy": 0.75,
                    }
                    metrics = trainer.train()

                assert metrics is not None
                assert len(metrics.train.losses) == 1

    @pytest.mark.skipif(not torch.cuda.is_available(), reason="CUDA not available")
    def test_device_selection_cuda_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test device selection integration when CUDA is available."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_cuda_device",
                "model_run_uuid": "test-cuda-device-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock select_device to return CUDA
            with patch("src.train.trainer.select_device") as mock_select_device:
                cuda_device = torch.device("cuda:0")
                mock_select_device.return_value = cuda_device

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Verify device selection was called
                mock_select_device.assert_called_once()

                # Verify trainer uses the selected device
                assert trainer.device == cuda_device

                # Verify model was moved to the correct device
                model_device = next(trainer.components.model.parameters()).device
                assert model_device == cuda_device

    @pytest.mark.skipif(
        not torch.backends.mps.is_available(), reason="MPS not available"
    )
    def test_device_selection_mps_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test device selection integration when MPS is available."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_mps_device",
                "model_run_uuid": "test-mps-device-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock select_device to return MPS
            with patch("src.train.trainer.select_device") as mock_select_device:
                mps_device = torch.device("mps")
                mock_select_device.return_value = mps_device

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Verify device selection was called
                mock_select_device.assert_called_once()

                # Verify trainer uses the selected device
                assert trainer.device == mps_device

                # Verify model was moved to the correct device (handle device index differences)
                model_device = next(trainer.components.model.parameters()).device
                assert model_device.type == mps_device.type

    def test_device_data_movement_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test that data is correctly moved to the selected device during training."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_data_movement",
                "model_run_uuid": "test-data-movement-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock select_device to return a specific device
            with patch("src.train.trainer.select_device") as mock_select_device:
                target_device = torch.device("cpu")
                mock_select_device.return_value = target_device

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Mock the training epoch to verify data movement
                data_devices = []

                def mock_train_epoch(epoch):  # pylint: disable=unused-argument
                    # Capture device of data during training
                    for batch_idx, (X, y) in enumerate(trainer.data_loaders["train"]):
                        data_devices.append((X.device, y.device))
                        if batch_idx >= 1:  # Only check first couple batches
                            break
                    return {"train_loss": 0.5, "train_accuracy": 0.8}

                with patch.object(
                    trainer, "_train_epoch", side_effect=mock_train_epoch
                ), patch.object(
                    trainer,
                    "_validate_epoch",
                    return_value={"validation_loss": 0.6, "validation_accuracy": 0.75},
                ), patch.object(
                    trainer.get_metrics_callback(), "_collect_resource_metrics"
                ):

                    trainer.train()

                # Verify data was moved to the correct device
                for x_device, y_device in data_devices:
                    assert x_device == target_device
                    assert y_device == target_device


class TestLoggingUtilsIntegration:
    """Integration tests for logging utilities with ModelTrainer."""

    def test_logging_setup_integration(self, utils_test_components, utils_test_loaders):
        """Test that logging is properly set up and integrated."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_logging_setup",
                "model_run_uuid": "test-logging-setup-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock setup_training_logger to verify it's called correctly
            with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
                mock_logger = MagicMock()
                mock_setup_logger.return_value = mock_logger

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Verify setup_training_logger was called with correct parameters
                mock_setup_logger.assert_called_once_with(
                    training_config["model_run_uuid"], training_config["model_id"]
                )

                # Verify trainer uses the returned logger
                assert trainer.logger == mock_logger

    def test_logging_during_training_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test that logging is used correctly during training."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_logging_training",
                "model_run_uuid": "test-logging-training-uuid",
                "run_output_dir": temp_dir,
                "epochs": 2,
            }

            # Mock setup_training_logger to capture log calls
            with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
                mock_logger = MagicMock()
                mock_setup_logger.return_value = mock_logger

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Run training to generate log calls (mocked for speed)
                mock_training_and_run(trainer)

                # Verify logger was used during training
                assert mock_logger.info.call_count > 0

                # Check for specific log messages
                log_calls = [call[0][0] for call in mock_logger.info.call_args_list]

                # Should have initialization logs
                init_logs = [
                    log for log in log_calls if "Initializing ModelTrainer" in log
                ]
                assert len(init_logs) > 0

                # Should have training start logs
                start_logs = [
                    log for log in log_calls if "Starting model training" in log
                ]
                assert len(start_logs) > 0

                # Should have epoch logs
                epoch_logs = [
                    log for log in log_calls if "Epoch" in log and "Train:" in log
                ]
                assert len(epoch_logs) == 2  # One per epoch

                # Should have completion logs
                finish_logs = [log for log in log_calls if "Training finished" in log]
                assert len(finish_logs) > 0

    def test_logging_error_handling_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test that logging works correctly during error handling."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_logging_errors",
                "model_run_uuid": "test-logging-errors-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            # Mock setup_training_logger to capture log calls
            with patch("src.train.trainer.setup_training_logger") as mock_setup_logger:
                mock_logger = MagicMock()
                mock_setup_logger.return_value = mock_logger

                config = TrainerConfig(
                    model_components=utils_test_components,
                    data_loaders=utils_test_loaders,
                    training_config=training_config,
                )

                trainer = ModelTrainer(config)

                # Simulate an error during training
                with patch.object(trainer, "_train_epoch") as mock_train:
                    mock_train.side_effect = RuntimeError("Test error")

                    metrics_callback = trainer.get_metrics_callback()
                    with patch.object(metrics_callback, "_collect_resource_metrics"):
                        # Training should handle the exception
                        with pytest.raises(RuntimeError):
                            trainer.train()

                # Verify error was logged
                assert mock_logger.error.call_count > 0
                error_calls = [call[0][0] for call in mock_logger.error.call_args_list]

                # Should have error logs
                error_logs = [
                    log for log in error_calls if "Unhandled exception" in log
                ]
                assert len(error_logs) > 0

    def test_logging_with_real_logger_integration(
        self, utils_test_components, utils_test_loaders
    ):
        """Test integration with actual logger setup (not mocked)."""
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "test_real_logger",
                "model_run_uuid": "test-real-logger-uuid",
                "run_output_dir": temp_dir,
                "epochs": 1,
            }

            config = TrainerConfig(
                model_components=utils_test_components,
                data_loaders=utils_test_loaders,
                training_config=training_config,
            )

            trainer = ModelTrainer(config)

            # Verify logger was created and is functional
            assert trainer.logger is not None
            assert hasattr(trainer.logger, "info")
            assert hasattr(trainer.logger, "error")
            assert hasattr(trainer.logger, "warning")

            # Run training to verify logger works (mocked for speed)
            metrics_callback = trainer.get_metrics_callback()
            with patch.object(trainer, "_train_epoch") as mock_train, patch.object(
                trainer, "_validate_epoch"
            ) as mock_validate, patch.object(
                metrics_callback, "_collect_resource_metrics"
            ):
                mock_train.return_value = {"train_loss": 0.5, "train_accuracy": 0.8}
                mock_validate.return_value = {
                    "validation_loss": 0.6,
                    "validation_accuracy": 0.75,
                }
                metrics = trainer.train()

            assert metrics is not None

            # Verify log files were created
            log_dir = Path(temp_dir) / "logs"
            if log_dir.exists():
                _ = list(log_dir.glob("*.log"))  # Check if log files exist
                # Log files may or may not be created depending on logger configuration
                # This is just to verify the integration doesn't break


# pylint: enable=duplicate-code
