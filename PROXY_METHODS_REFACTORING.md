# Proxy Methods Refactoring - Outstanding Work

This document outlines the remaining useless proxy methods and functions identified in the codebase that should be evaluated for removal or refactoring.

## ✅ Completed

### Removed Methods
- **`get_coin_side_labels()`** in `src/datasets/image_dataset.py` - ✅ **REMOVED**
  - Was a complete duplicate of `get_label_mapping()`
  - Not referenced anywhere in production code or tests
  - Eliminated unnecessary code duplication

- **`get_supported_image_extensions()`** in `src/utils/image_utils.py` - ✅ **REMOVED**
  - Was a useless proxy that just returned `SUPPORTED_IMAGE_EXTENSIONS.copy()`
  - Only had 2 callers, both updated to use the constant directly
  - Inconsistent pattern - same file used direct access internally
  - Removed unnecessary `.copy()` operations where not needed
  - **Refactoring details:**
    - Updated `src/datasets/image_dataset.py` to import and use `SUPPORTED_IMAGE_EXTENSIONS` directly
    - Updated `src/utils/image_utils.py` internal usage to use constant directly (no copy needed)
    - All tests pass after refactoring

## 🔍 Outstanding Work - Requires Evaluation

### 1. Metrics Property Proxies in `src/train/metrics.py`

**Status:** Needs investigation for backward compatibility

**Methods to evaluate:**
```python
@property
def train(self) -> TrainingMetrics:
    """Access training metrics."""
    return self.storage.train

@property  
def test(self) -> TestingMetrics:
    """Access test metrics."""
    return self.storage.test

@property
def classification(self) -> ClassificationMetrics:
    """Access classification metrics."""
    return self.storage.classification

@property
def final_test(self) -> FinalTestMetrics:
    """Access final test metrics."""
    return self.storage.final_test

@property
def timing(self) -> MetricsDict[List[float]]:
    """Access timing metrics."""
    return self.storage.timing

@property
def resources(self) -> MetricsDict[List[float]]:
    """Access resource metrics."""
    return self.storage.resources
```

**Analysis needed:**
- [ ] Check if these properties are used for backward compatibility
- [ ] Search codebase for usage patterns: `metrics.train` vs `metrics.storage.train`
- [ ] Determine if direct access to `storage` attributes would break existing code
- [ ] Consider deprecation strategy if removal is desired

**Recommendation:** If these are purely for convenience and not backward compatibility, consider removing and updating callers to use `metrics.storage.train` directly.

### 2. Database Service Proxy Methods in `src/database/services/base_service.py`

**Status:** Needs evaluation for added value

**Methods to evaluate:**
```python
@classmethod
def create_model(cls, data: Dict[str, Any], model_class: Optional[Type[BaseModel]] = None, partial: bool = False) -> BaseModel:
    # Parameter defaulting logic + validation
    return ModelFactory.create_model(model_class=model_class, data=data, partial=partial)

@classmethod  
def create_models_from_list(cls, data_list: List[Dict[str, Any]], model_class: Optional[Type[BaseModel]] = None, partial: bool = False, skip_invalid: bool = False) -> List[BaseModel]:
    # Parameter defaulting logic + validation
    return ModelFactory.create_models_from_list(model_class=model_class, data_list=data_list, partial=partial, skip_invalid=skip_invalid)
```

**Analysis needed:**
- [ ] Check if the parameter defaulting logic (using `cls.model_class`) adds significant value
- [ ] Determine if callers could use `ModelFactory` directly
- [ ] Evaluate if the validation logic justifies the proxy layer
- [ ] Consider if this violates DRY principles vs providing useful abstraction

**Recommendation:** If the defaulting logic is minimal and callers could easily use `ModelFactory` directly, consider removing these proxies.







## 🧪 Test-Only Methods - Keep

These methods are only used in tests and provide testing utilities:

### `tests/api/routes/test_helpers.py`
- **`get_test_data()`** - ✅ **KEEP** (test utility)

### `tests/unit/common/callbacks/test_helpers.py`  
- **`notify_callbacks()`** - ✅ **KEEP** (test utility for accessing protected methods)

## 📋 Action Items

### Immediate Next Steps
1. **Investigate Metrics Properties**
   - [ ] Search for usage patterns of `metrics.train`, `metrics.test`, etc.
   - [ ] Check git history for when these properties were added
   - [ ] Determine if they're part of a public API

2. **Evaluate Database Service Methods**
   - [ ] Analyze the parameter defaulting logic value
   - [ ] Check if `ModelFactory` could be used directly by callers
   - [ ] Review inheritance hierarchy to see if these methods are overridden

3. ~~**Review Image Utils Function**~~ - ✅ **COMPLETED**
   - ✅ Checked all callers of `get_supported_image_extensions()`
   - ✅ Determined direct constant access is cleaner
   - ✅ Verified `.copy()` was unnecessary for most use cases

### Long-term Considerations
- **Establish proxy method guidelines** - Define when proxy methods are acceptable vs when they should be avoided
- **Code review checklist** - Add proxy method detection to code review process
- **Automated detection** - Consider adding linting rules to detect simple proxy methods

## 🎯 Success Criteria

- [ ] All identified proxy methods have been evaluated
- [ ] Decisions made on keep/remove for each method
- [ ] Codebase follows consistent patterns for method delegation
- [ ] No unnecessary code duplication remains
- [ ] DRY principles are maintained without sacrificing useful abstractions

---

**Note:** This refactoring aligns with the project's preference for removing redundancy and ensuring consistent behavior patterns across modules while maintaining code simplicity.
